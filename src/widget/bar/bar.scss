/* Base styles for the bar */
@use "../../styles/colors";

top-bar {
  background-color: colors.c(background);
  color: colors.c(foreground);
  border-bottom: 1px solid colors.c(border);
}


/* Bar content styling */
bar-group {
  background-color: colors.c(background-alt);
  border-radius: 6px;
  margin: 4px;
  padding: 2px 8px;
}

bar-group-standalone {
  background-color: colors.c(background-elevated);
  border-radius: 24px;
  padding: 4px 4px;
}

bar-group-margin {
  margin: 4px;
  padding: 0;
}

bar-group-pad {
  padding: 2px 8px;
}

/* Workspace indicator */
bar-ws {
  margin: 2px;
  border-radius: 50%;
  padding: 0;
  min-width: 12px;
  min-height: 12px;
  font-size: 9px;
}

bar-ws-active {
  background-color: colors.c(primary);
  color: colors.c(background);
}

bar-ws-occupied {
  background-color: colors.c(overlay);
  color: colors.c(foreground-alt);
}

/* Corner elements */
// corner {
//   background-color: colors.c(background);
//   border-radius: 24px;
// }


/* Window styling for corner windows */
corner-window {
  background-color: transparent;
}

.resource-icon-spacing {
  margin-left: 4px;
  margin-top: 10px;
  padding-top: 10px;
}


/* System resources styling */
.bar-ram-circprog,
.bar-swap-circprog,
.bar-cpu-circprog,
.bar-battery-circprog {
  min-width: 24px;
  min-height: 24px;
  margin: 0;
  padding: 0;
}

/* Additional styling for specific resource types */
.bar-ram-icon,
.bar-swap-icon,
.bar-cpu-icon {
  margin: 0;
  padding: 0;
}

.bar-ram-txt,
.bar-swap-txt,
.bar-cpu-txt {
  font-size: 10px;
  margin-left: 4px;
  color: colors.c(foreground-alt);
}

/* System resource button styling */
.sys-resources-btn {
  background: transparent;
  border: none;
  border-radius: 6px;

  &:hover {
    background: colors.c(hover);
  }

  &:active {
    background: colors.c(active);
  }
}

/* Clock module styling */
.bar-clock-box {
  padding: 2px 4px;
}

.bar-time {
  font-weight: bold;
  font-family: 'Sans';
  font-size: 12px;
  color: colors.c(foreground);
}

.bar-date {
  color: colors.c(foreground-alt);
  margin-left: 2px;
}

.bar-wintitle-txt {
  margin-top: 4px;
  color: colors.c(foreground-alt);
  font-size: 11px;
}

bar-sidemodule {
  border-radius: 14px;
  margin: 5px;
  background-color: colors.c(background-elevated);
}

bar-sidemodule-btn {
  background: transparent;
  border-radius: 6px;
  padding: 4px;

  &:hover {
    background: colors.c(hover);
  }

  &:active {
    background: colors.c(active);
  }
}

bar-group-margin {
  margin: 4px;
}


bar-corner-spacing {
  margin-left: 10px;
  padding-left: 5px;
}

bar-space-button {
  padding-top: 2px;
  padding-left: 10px;
}

/* Music module styles */
.bar-music-btn {
  background: transparent;
  border: none;
  padding: 0;
  margin: 0;

  &:hover bar-group {
    background-color: colors.c(hover);
  }
}

.bar-music-playstate,
.bar-music-playstate-playing {
  transition: all 300ms ease;
}

.bar-music-icon {
  color: colors.c(foreground);
  font-size: 14px;
}

.bar-music-progress {
  color: colors.c(primary);
}

.bar-music-title {
  color: colors.c(foreground);
  font-size: 12px;
  font-weight: 500;
  margin-left: 6px;
}

.bar-music-artist {
  color: colors.c(foreground-alt);
  font-size: 11px;
  margin-left: 4px;
}


@keyframes pulse-music {
  0% {
    transform: translateX(0) translateY(0) rotate(0deg);
  }

  25% {
    transform: translateX(1px) translateY(-1px) rotate(5deg);
  }

  50% {
    transform: translateX(0) translateY(0) rotate(0deg);
  }

  75% {
    transform: translateX(-1px) translateY(-1px) rotate(-5deg);
  }

  100% {
    transform: translateX(0) translateY(0) rotate(0deg);
  }
}

bar-music-playstate-icon {
  &.playing-animation {
    animation: pulse-music 1.5s ease-in-out infinite;
  }
}
