import { Widget } from "astal/gtk4";
import { barLogger as log } from "../../../../utils/logger";

export interface TrayModuleProps extends Widget.BoxProps { }

export default function Tray(_props: TrayModuleProps) {
  // Temporarily disabled until AstalTray is installed
  log.warn("AstalTray not available - tray module disabled");

  return (
    <box cssName="margin-right-5 spacing-h-15">
      {/* Tray items will appear here when AstalTray is installed */}
    </box>
  );
}
