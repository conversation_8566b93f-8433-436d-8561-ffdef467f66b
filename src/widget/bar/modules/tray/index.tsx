import { Widget, Gtk, } from "astal/gtk4";
import { Variable, bind } from "astal";
import config from "../../../../utils/config";
import { barLogger as log } from "../../../../utils/logger";

export interface TrayModuleProps extends Widget.BoxProps { }

// Helper function to safely import AstalTray
function getTrayModule() {
  try {
    // Dynamic import to handle missing AstalTray gracefully
    const SystemTray = require("gi://AstalTray");
    return SystemTray;
  } catch (error) {
    log.info("AstalTray not available - tray module disabled");
    return null;
  }
}

export default function Tray(trayModuleProps: TrayModuleProps) {
  // const { ...props } = trayModuleProps;

  const SystemTray = getTrayModule();

  // If AstalTray is not available, return an empty invisible box
  if (!SystemTray) {
    log.debug("Tray module disabled - AstalTray not installed");
    return <box visible={false} />;
  }

  // Import TrayItem only if SystemTray is available
  let TrayItem;
  try {
    TrayItem = require("./item").default;
  } catch (error) {
    log.error("Failed to load TrayItem component", error);
    return <box visible={false} />;
  }

  const tray = SystemTray.get_default();
  const trayItems = Variable<any[]>(tray.get_items());

  tray.connect("item-added", () => {
    log.debug("Tray item added");
    trayItems.set(tray.get_items());
  });

  tray.connect("item-removed", () => {
    log.debug("Tray item removed");
    trayItems.set(tray.get_items());
  });

  return (
    <box>
      <revealer
        reveal-child={true}
        transition-type={Gtk.RevealerTransitionType.SLIDE_DOWN}
        transition-duration={config.animations.durationLarge}
      >
        <box cssName="margin-right-5 spacing-h-15">
          {bind(trayItems).as((v: any[]) =>
            v.map((item) => <TrayItem item={item} />),
          )}
        </box>
      </revealer>
    </box>
  );
}
