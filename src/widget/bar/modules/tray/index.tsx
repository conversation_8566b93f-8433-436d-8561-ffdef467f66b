import { Widget, Gtk, } from "astal/gtk4";
import { Variable, bind } from "astal";
import config from "../../../../utils/config";
import { barLogger as log } from "../../../../utils/logger";
import { getTray, isTrayAvailable } from "../../../../utils/tray-helper";
import TrayItem from "./item";

export interface TrayModuleProps extends Widget.BoxProps { }

export default function Tray(_props: TrayModuleProps) {
  // Check if tray is available
  if (!isTrayAvailable()) {
    log.debug("Tray module disabled - AstalTray not installed");
    return <box visible={false} />;
  }

  const tray = getTray();
  const trayItems = Variable<any[]>(tray.get_items());

  tray.connect("item-added", () => {
    log.debug("Tray item added");
    trayItems.set(tray.get_items());
  });

  tray.connect("item-removed", () => {
    log.debug("Tray item removed");
    trayItems.set(tray.get_items());
  });

  return (
    <box>
      <revealer
        revealChild={true}
        transitionType={Gtk.RevealerTransitionType.SLIDE_DOWN}
        transitionDuration={config.animations.durationLarge}
      >
        <box cssName="margin-right-5 spacing-h-15">
          {bind(trayItems).as((v: any[]) =>
            v.map((item) => <TrayItem item={item} />),
          )}
        </box>
      </revealer>
    </box>
  );
}
