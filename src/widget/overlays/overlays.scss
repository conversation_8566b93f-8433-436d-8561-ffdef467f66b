@use "../../styles/colors";

popup-notifications-window,
indicators-window {
  background-color: transparent;
  border: none;
}

indicators-window {
  margin-bottom: 20px;
}

indicator-wrapper {

  .show {
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
  }

  .hide {
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
  }
}

indicator-container {
  margin: 8px;
  border-radius: 16px;
  background-color: colors.c(background-elevated, 0.3);
  padding: 6px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.25);
  border: 1px solid colors.c(border);

  .show {
    opacity: 1;
    transition: opacity 0.4s ease-in-out;
  }

  .hide {
    opacity: 0;
    transition: opacity 0.4s ease-in-out;
  }

}

osd-brightness {
  background-color: colors.c(overlay);
  border-radius: 8px;
  padding: 4px;
  margin: 4px;
}

osd-kbb-brightness {
  background-color: colors.c(overlay);
  border-radius: 8px;
  padding: 4px;
  margin: 4px;
}

osd-volume {
  background-color: colors.c(overlay);
  padding: 4px;
  border-radius: 8px;
  margin: 4px;
}

osd-indicator-card {
  background-color: colors.c(overlay);
  border-radius: 24px;
  padding: 8px;
  margin: 4px;
}


osd-label {
  font-size: 10px;
  color: colors.c(foreground-alt);
}

osd-icon {
  padding: 0 4px;

  image {
    color: colors.c(foreground);
  }
}


osd-value-txt {
  font-size: 12px;
  color: colors.c(foreground-alt);
}



.osd-value {
  padding: 4px;
  margin: 4px;
}

.osd-bg {
  background-color: colors.c(background-elevated);
}

.progress-bar-container {
  background-color: colors.c(muted);
  border-radius: 8px;
  height: 8px;
  margin: 4px 0;

  progress-bar-fill {
    background-color: colors.c(primary);
    border-radius: 8px;
    transition: all 0.3s ease-in-out;
    min-height: 8px;
  }
}

// Popup notification specific styles
.popup-notifications-container {
  margin: 0;
  padding: 10px 0 0 0;
  background: transparent;
}

// Notification hover container - sized to fit notifications
.notification-hover-container {
  min-width: 430px;
  min-height: 150px;
  height: 150px;

  &:hover {
    min-height: 480px; // Expand height when hovering to accommodate fanned layout
  }

  transition: min-height 400ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

// Base notification item styling
.notification-stack-item {
  transition: all 400ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

// Notification positions - these are wrapper boxes in the overlay
.notification-position-0,
.notification-position-1,
.notification-position-2,
.notification-position-3 {
  transition: all 400ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

// Stacked state - notifications overlap
.notification-position-0.stacked {
  margin-top: 0;
}

.notification-position-1.stacked {
  margin-top: 10px; // Small offset to show stacking
}

.notification-position-2.stacked {
  margin-top: 20px;
}

.notification-position-3.stacked {
  margin-top: 30px;
}

// Fanned out state - notifications spread out  
.notification-position-0.fanned {
  margin-top: 0;
}

.notification-position-1.fanned {
  margin-top: 110px;
}

.notification-position-2.fanned {
  margin-top: 220px;
}

.notification-position-3.fanned {
  margin-top: 330px;
}

.popup-notification {
  min-width: 400px;
  max-width: 420px;
  margin-bottom: 2px;
  margin-right: 10px;
  background-color: colors.c(background-elevated);
  background-image: linear-gradient(135deg,
      colors.c(surface, 1) 0%,
      transparent 100%);
  border: 1px solid colors.c(border-alt);
  border-radius: 12px;
  box-shadow:
    0 10px 30px colors.c(base, 0.5),
    0 5px 15px colors.c(base, 0.4),
    inset 0 1px 0 colors.c(hover, 0.1);
  transition: all 200ms ease;
  cursor: pointer;

  &:hover {
    transform: scale(1.02);
    box-shadow:
      0 12px 32px colors.c(base, 0.4),
      0 6px 16px colors.c(base, 0.3),
      inset 0 1px 0 colors.c(surface, 0.15);

    .popup-notification-close {
      opacity: 1;
    }
  }

  &.critical {
    border-color: colors.c(error, 0.6);
    background: linear-gradient(135deg,
        colors.darken(error, 90%) 0%,
        colors.c(background-elevated) 100%);
  }

  &.low {
    opacity: 0.95;
  }
}

.popup-notification-content {
  padding: 16px;
}

.popup-notification-icon {
  min-width: 32px;

  image,
  .phosphor-icon {
    color: colors.c(foreground-alt);
  }
}

.popup-notification-header {
  margin-bottom: 4px;
}

.popup-notification-title {
  font-weight: 600;
  font-size: 14px;
  color: colors.c(foreground);
}

.popup-notification-close {
  padding: 4px;
  background: transparent;
  border: none;
  border-radius: 6px;
  opacity: 0;
  transition: all 200ms ease;

  &:hover {
    background: colors.c(overlay, 0.1);
  }

  .phosphor-icon {
    color: colors.c(foreground-alt);
  }
}

.popup-notification-body {
  font-size: 13px;
  color: colors.c(foreground-alt);
  line-height: 1.4;
}

.popup-notification-actions {
  margin-top: 12px;
}

.popup-notification-action {
  padding: 6px 12px;
  background: colors.c(overlay, 0.1);
  border: 1px solid colors.c(border, 0.2);
  border-radius: 8px;
  font-size: 12px;
  color: colors.c(foreground);
  transition: all 150ms ease;

  &:hover {
    background: colors.c(overlay, 0.2);
    border-color: colors.c(accent, 0.3);
    transform: translateY(-1px);
  }

  &:active {
    transform: scale(0.98);
  }
}

// Stacking animation
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-100px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(100px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.popup-notification {
  animation: slideDown 300ms ease-out;
}

// Music Overlay Styles
music-overlay-window {
  background: transparent;
  margin: 50px;
  margin-bottom: 100px;
  margin-top: 10px;
}

music-overlay-container {
  padding: 0;
  margin: 0;
}

music-overlay-empty {
  margin: 0 16px;
  padding: 12px 20px;
  background: colors.c(background-elevated, 0.85);
  backdrop-filter: blur(24px) saturate(200%);
  -webkit-backdrop-filter: blur(24px) saturate(200%);
  border-radius: 20px;
  border: 1px solid colors.c(border, 0.15);
  box-shadow:
    0 20px 60px colors.c(base, 0.6),
    0 8px 24px colors.c(base, 0.4),
    0 2px 8px colors.c(base, 0.2),
    inset 0 1px 0 colors.c(text, 0.05),
    inset 0 -1px 0 colors.c(base, 0.3);

  .phosphor-icon {
    color: colors.c(primary, 0.8);
  }

  label {
    color: colors.c(foreground-alt);
    font-size: 14px;
    font-weight: 500;
  }
}

music-controls {
  margin-left: 0;
  margin-right: 16px;
  padding: 16px;
  background: colors.c(background-elevated, 0.85);
  backdrop-filter: blur(24px) saturate(200%);
  -webkit-backdrop-filter: blur(24px) saturate(200%);
  border-radius: 20px;
  border: 1px solid colors.c(border, 0.15);
  box-shadow:
    0 20px 60px colors.c(base, 0.6),
    0 8px 24px colors.c(base, 0.4),
    0 2px 8px colors.c(base, 0.2),
    inset 0 1px 0 colors.c(text, 0.05),
    inset 0 -1px 0 colors.c(base, 0.3);
  min-width: 380px;
  max-width: 420px;

  // Subtle glass shimmer effect
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent 0%,
        colors.c(text, 0.03) 50%,
        transparent 100%);
    animation: shimmer 8s ease-in-out infinite;
  }
}

@keyframes shimmer {

  0%,
  100% {
    transform: translateX(-100%);
  }

  50% {
    transform: translateX(200%);
  }
}

music-album-art-container {
  min-width: 100px;
  min-height: 100px;
  margin-right: 12px;
  border-radius: 14px;
}

music-album-art {
  min-width: 100px;
  min-height: 100px;
  max-width: 100px;
  max-height: 100px;
  border-radius: 14px;
  box-shadow:
    0 10px 24px colors.c(base, 0.4),
    0 3px 10px colors.c(base, 0.2);
  border: 1px solid colors.c(border, 0.2);
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.03);
  }
}

music-album-art-placeholder {
  width: 100px;
  height: 100px;
  border-radius: 14px;
  background: linear-gradient(135deg,
      colors.c(primary, 0.1) 0%,
      colors.c(secondary, 0.05) 100%);
  border: 1px solid colors.c(border, 0.2);
  box-shadow:
    0 6px 18px colors.c(base, 0.3),
    inset 0 1px 0 colors.c(text, 0.05);

  .phosphor-icon {
    color: colors.c(primary, 0.6);
  }
}

music-info-controls {
  min-width: 240px;
  padding-left: 6px;
}

music-track-info {
  margin-bottom: 8px;
}

music-title {
  font-size: 15px;
  font-weight: 600;
  color: colors.c(text);
  margin-bottom: 2px;
  letter-spacing: -0.01em;
  line-height: 1.3;
}

music-artist {
  font-size: 13px;
  font-weight: 500;
  color: colors.c(subtle);
  margin-bottom: 2px;
}

music-album {
  font-size: 11px;
  color: colors.c(muted);
  font-weight: 400;
}


music-progress-bar {
  min-height: 6px;
  background: colors.c(muted, 0.4);
  border-radius: 3px;
  min-width: 200px;
  border: 1px solid colors.c(border, 0.15);
  box-shadow:
    inset 0 1px 3px colors.c(base, 0.4),
    0 1px 0 colors.c(surface, 0.15);
}

music-progress-fill {
  min-height: 6px;
  max-height: 6px;
  background: linear-gradient(90deg,
      colors.c(primary) 0%,
      colors.c(accent) 100%);
  border-radius: 3px;
  transition: min-width 0.3s ease;
  box-shadow:
    0 0 10px colors.c(primary, 0.5),
    0 2px 4px colors.c(base, 0.3),
    inset 0 1px 0 colors.c(accent, 0.4);
  margin: 0;
  padding: 0;
}

music-time-labels {
  margin-top: 2px;
  min-width: 150px; // Match the progress bar width
}

music-time {
  font-size: 10px;
  color: colors.c(subtle);
  font-variant-numeric: tabular-nums;
  font-weight: 500;
}

music-progress-section {
  min-height: 32px; // Ensure consistent height
  transform: translateY(-6px);

}

music-progress-wrapper {
  min-width: 150px;

  fixed {
    min-width: 150px;


    min-height: 6px;
  }
}

music-control-buttons {
  padding: 0;
  // border: 1px solid red;
  background: transparent;
  transform: translateY(-10px);
}

music-control-button {
  padding: 8px;
  background: colors.c(surface, 0.3);
  border: 1px solid colors.c(border, 0.15);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 2px 6px colors.c(base, 0.2),
    0 1px 2px colors.c(base, 0.1);

  &:hover:not(:disabled) {
    background: colors.c(overlay, 0.4);
    border-color: colors.c(primary, 0.3);
    transform: translateY(-2px) scale(1.05);
    box-shadow:
      0 4px 12px colors.c(base, 0.3),
      0 2px 4px colors.c(base, 0.2);
  }

  &:active:not(:disabled) {
    transform: translateY(0) scale(0.98);
    box-shadow:
      0 1px 4px colors.c(base, 0.2),
      inset 0 1px 2px colors.c(base, 0.1);
  }

  &:disabled {
    opacity: 0.35;
    cursor: not-allowed;
  }

  .phosphor-icon {
    color: colors.c(text);
  }
}


music-control-button-small {
  padding: 0;
  width: 28px;
  height: 28px;
  background: colors.c(surface, 0.25);
  border: 1px solid colors.c(border, 0.12);
  border-radius: 32px; // Half of width/height
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 1px 3px colors.c(base, 0.15),
    0 1px 2px colors.c(base, 0.08);

  box {
    padding: 6px;
  }

  &:hover:not(:disabled) {
    background: colors.c(overlay, 0.35);
    border-color: colors.c(primary, 0.25);
    transform: scale(1.08);
    box-shadow:
      0 2px 6px colors.c(base, 0.25),
      0 1px 3px colors.c(base, 0.15);
  }

  &:active:not(:disabled) {
    transform: scale(0.95);
    box-shadow:
      0 1px 2px colors.c(base, 0.15),
      inset 0 1px 2px colors.c(base, 0.08);
  }

  &:disabled {
    opacity: 0.3;
    cursor: not-allowed;
  }

  .phosphor-icon {
    color: colors.c(text);
  }

  &.music-play-pause {
    background: linear-gradient(135deg,
        colors.c(primary, 0.9) 0%,
        colors.c(accent, 0.9) 100%);
    border-color: colors.c(primary, 0.2);
    border-radius: 16px;

    box {
      padding: 6px;
    }

    &:hover:not(:disabled) {
      background: linear-gradient(135deg,
          colors.c(primary) 0%,
          colors.c(accent) 100%);
      box-shadow:
        0 3px 8px colors.c(primary, 0.35),
        0 1px 3px colors.c(base, 0.2);
    }

    .phosphor-icon {
      color: colors.c(on-primary);
    }
  }
}

music-close-button {
  padding: 4px;
  background: colors.c(surface, 0.2);
  border: 1px solid colors.c(border, 0.1);
  border-radius: 6px;
  opacity: 0.6;
  transition: all 0.2s ease;
  margin-left: 4px;

  &:hover {
    opacity: 1;
    background: colors.c(overlay, 0.3);
    border-color: colors.c(border, 0.2);
    transform: scale(1.05);
  }

  .phosphor-icon {
    color: colors.c(subtle);
  }
}

// Desktop Wallpaper Window Styles
desktop-wallpaper-window {
  background: transparent;
}

desktop-wallpaper-container {
  margin: 0 auto 12px;
  padding: 8px;
  width: 1200px;
  background: colors.c(background-elevated, 0.9);
  backdrop-filter: blur(32px) saturate(180%);
  -webkit-backdrop-filter: blur(32px) saturate(180%);
  border-radius: 20px;
  border: 1px solid colors.c(border, 0.12);
  box-shadow:
    0 16px 48px colors.c(base, 0.3),
    0 6px 20px colors.c(base, 0.2),
    inset 0 1px 0 colors.c(text, 0.03),
    inset 0 -1px 0 colors.c(base, 0.2);
}

wallpaper-header {
  padding-bottom: 0px;
  border-bottom: 1px solid colors.c(border, 0.1);
}

wallpaper-title {
  font-size: 18px;
  font-weight: 600;
  color: colors.c(foreground);
  letter-spacing: -0.02em;
}

wallpaper-close-btn {
  padding: 4px;
  background: colors.c(surface, 0.08);
  border: 1px solid colors.c(border, 0.06);
  border-radius: 6px;
  transition: all 0.2s ease;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);

  &:hover {
    background: colors.c(overlay, 0.15);
    border-color: colors.c(border, 0.12);
    transform: scale(1.05);
  }

  image {
    color: colors.c(foreground-alt);
  }
}

wallpaper-carousel {
  padding: 12px 0;
}

carousel-nav-btn {
  padding: 8px;
  background: colors.c(surface, 0.1);
  border: 1px solid colors.c(border, 0.08);
  border-radius: 10px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 1px 4px colors.c(base, 0.1),
    0 1px 2px colors.c(base, 0.05);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);

  &:hover {
    background: colors.c(overlay, 0.2);
    border-color: colors.c(primary, 0.2);
    transform: scale(1.05);
    box-shadow:
      0 3px 8px colors.c(base, 0.2),
      0 1px 3px colors.c(base, 0.1);
  }

  &:focus {
    background: colors.c(foreground, 0.2);
    border-color: colors.c(border-alt, 0.2);
    transform: scale(1.05);
    box-shadow:
      0 3px 8px colors.c(base, 0.2),
      0 1px 3px colors.c(base, 0.1);
  }

  &:active {
    border-color: colors.c(border-alt, 0.9);
    transform: scale(0.98);
  }

  &:disabled {
    opacity: 0.2;
    cursor: not-allowed;
  }

  image {
    color: colors.c(foreground-alt);
  }
}

wallpaper-items {
  min-height: 140px;
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

wallpaper-item {
  padding: 0;
  background: colors.c(surface, 0.08);
  border: 1px solid colors.c(border, 0.08);
  border-radius: 14px;
  transition: all 0.2s ease;
  overflow: hidden;

  &:hover {
    background: colors.c(overlay, 0.15);
    border-color: colors.c(border, 0.15);
    transform: translateY(-2px) scale(1.02);
    box-shadow:
      0 6px 12px colors.c(base, 0.2),
      0 3px 6px colors.c(base, 0.1);
  }

  &:active {
    transform: translateY(0) scale(0.98);
  }

  &:focus {
    outline: none;
    background: colors.c(overlay, 0.15);
    border-color: colors.c(border-alt, 0.95);
    transform: translateY(-2px) scale(1.02);
    box-shadow:
      0 6px 12px colors.c(base, 0.2),
      0 3px 6px colors.c(base, 0.1);
  }

  &.selected {
    border-color: colors.c(primary, 0.8);
    background: colors.c(primary, 0.08);
    box-shadow:
      0 0 16px colors.c(primary, 0.2),
      0 3px 8px colors.c(base, 0.15);
  }
}

wallpaper-thumbnail {
  // Image fills the container
}

.wallpaper-image {
  border-radius: 14px;
  padding: 0;
  margin: 0;
}

wallpaper-selected-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: colors.c(primary, 0.6);
  border-radius: 10px;
  backdrop-filter: blur(12px) saturate(150%);
  -webkit-backdrop-filter: blur(12px) saturate(150%);

  image {
    color: white;
    filter: drop-shadow(0 2px 4px colors.c(base, 0.3));
  }
}

wallpaper-name {
  font-size: 12px;
  color: colors.c(foreground-alt);
  margin-top: 4px;
}

wallpaper-footer {
  padding-top: 8px;
  border-top: 1px solid colors.c(border, 0.1);
}

page-indicator {
  font-size: 12px;
  color: colors.c(foreground-alt);
  font-weight: 500;
  margin-bottom: 8px;
  margin-left: 8px;
}

// Animation for wallpaper carousel
@keyframes wallpaperSlide {
  from {
    opacity: 0;
    transform: translateX(20px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

wallpaper-thumbnail-container {
  border-radius: 14px;
  padding: 0;
  margin: 0;
  // padding-top: 8px;
  // padding-left: 5px;
  // padding-right: 5px;
}

wallpaper-item {
  animation: wallpaperSlide 0.3s ease-out;
  animation-fill-mode: both;
  margin: 0;
  padding: 0;

  @for $i from 1 through 5 {
    &:nth-child(#{$i}) {
      animation-delay: #{$i * 0.05}s;
    }
  }
}
