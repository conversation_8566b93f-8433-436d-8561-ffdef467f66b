@use "../../styles/colors";

settings-window {
  background: colors.c(base, 0.3); // Semi-transparent overlay
}

settings-window-wrapper {
  padding: 40px; // Add padding around the container
  background: transparent;
  min-height: 600px; // Subtract the header height
  min-width: 300px;
}

settings-container {
  margin: auto;
  width: 600px;
  height: 500px;
  max-height: 90%;
  background: colors.c(background-elevated, 0.85);
  backdrop-filter: blur(24px) saturate(200%);
  -webkit-backdrop-filter: blur(24px) saturate(200%);
  border: 1px solid colors.c(border, 0.15);
  border-radius: 20px;
  box-shadow:
    0 20px 60px colors.c(base, 0.6),
    0 8px 24px colors.c(base, 0.4),
    0 2px 8px colors.c(base, 0.2),
    inset 0 1px 0 colors.c(text, 0.05),
    inset 0 -1px 0 colors.c(base, 0.3);

  // Add subtle shimmer effect
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(90deg,
        transparent 0%,
        colors.c(text, 0.03) 50%,
        transparent 100%);
    animation: shimmer 8s ease-in-out infinite;
    pointer-events: none;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) rotate(30deg);
  }

  100% {
    transform: translateX(100%) rotate(30deg);
  }
}

settings-header {
  padding: 16px 20px;
  border-bottom: 1px solid colors.c(border, 0.1);
  background: linear-gradient(to bottom,
      colors.c(background-elevated, 0.2),
      transparent);
}

settings-search-box {
  background: colors.c(background-alt, 0.3);
  border: 1px solid colors.c(border, 0.1);
  border-radius: 12px;
  padding: 8px 12px;

  label {
    font-size: 14px;
    color: colors.c(text, 0.6);
  }
}

settings-search {
  background: transparent;
  border: none;
  color: colors.c(text);
  font-size: 14px;

  &:focus {
    outline: none;
  }

  selection {
    background: colors.c(primary, 0.3);
    color: colors.c(text);
  }
}

settings-title {
  font-size: 18px;
  font-weight: 600;
  color: colors.c(text);
}

settings-close-button {
  background: colors.c(background-alt, 0.3);
  border: 1px solid colors.c(border, 0.1);
  border-radius: 8px;
  padding: 4px 8px;
  min-width: 28px;
  min-height: 28px;
  color: colors.c(text, 0.7);
  transition: all 150ms ease;

  &:hover {
    background: colors.c(background-alt, 0.5);
    border-color: colors.c(border, 0.2);
    color: colors.c(text);
    box-shadow: 0 2px 8px colors.c(base, 0.2);
  }

  &:active {
    background: colors.c(background-alt, 0.6);
    transform: scale(0.95);
  }

  label {
    font-size: 16px;
  }
}

settings-scrollable {
  background: transparent;
  min-height: 0; // Allow shrinking

  scrollbar {
    background: transparent;
    border: none;

    &:hover {
      background: colors.c(background-alt, 0.1);
    }

    slider {
      background: colors.c(text, 0.2);
      border-radius: 4px;
      min-width: 6px;

      &:hover {
        background: colors.c(text, 0.3);
      }
    }
  }
}

settings-content {
  padding: 20px;
}

settings-section {
  background: colors.c(background-alt, 0.2);
  border: 1px solid colors.c(border, 0.08);
  border-radius: 12px;
  padding: 16px;
  transition: all 200ms ease;

  &:hover {
    background: colors.c(background-alt, 0.25);
    border-color: colors.c(border, 0.12);
    box-shadow: 0 4px 12px colors.c(base, 0.1);
  }
}

settings-section-title {
  font-size: 14px;
  font-weight: 600;
  color: colors.c(primary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
}

settings-section-content {
  padding: 0 4px;
}

setting-row {
  padding: 8px;
  border-radius: 8px;
  transition: background 150ms ease;
  width: 300px;

  &:hover {
    background: colors.c(background-elevated, 0.3);
  }

  &.indented {
    margin-left: 20px;
    padding-left: 12px;
    border-left: 2px solid colors.c(border, 0.1);
  }
}

setting-row-control {
  min-width: 80px; // Ensure controls have minimum width
}

setting-label {
  font-size: 14px;
  font-weight: 500;
  color: colors.c(text);
  margin-bottom: 2px;
}

setting-description {
  font-size: 12px;
  color: colors.c(text, 0.6);
  line-height: 1.4;
}

// Form controls styling
switch {
  background: colors.c(background-alt, 0.5);
  border: 1px solid colors.c(border, 0.2);
  border-radius: 12px;
  min-width: 44px;
  min-height: 24px;
  padding: 2px;

  &:checked {
    background: colors.c(primary, 0.8);
    border-color: colors.c(primary);

    slider {
      background: colors.c(text);
    }
  }

  slider {
    background: colors.c(text, 0.5);
    border-radius: 10px;
    min-width: 20px;
    min-height: 20px;
    transition: all 150ms ease;
  }
}

spinbutton {
  background: colors.c(background-alt, 0.3);
  border: 1px solid colors.c(border, 0.15);
  border-radius: 8px;
  padding: 6px 8px;
  color: colors.c(text);
  font-size: 13px;

  &:hover {
    background: colors.c(background-alt, 0.4);
    border-color: colors.c(border, 0.25);
  }

  &:focus {
    border-color: colors.c(primary, 0.5);
    box-shadow: 0 0 0 2px colors.c(primary, 0.2);
  }

  button {
    background: transparent;
    border: none;
    color: colors.c(text, 0.6);
    padding: 0 4px;

    &:hover {
      color: colors.c(text);
    }
  }
}

entry {
  background: colors.c(background-alt, 0.3);
  border: 1px solid colors.c(border, 0.15);
  border-radius: 8px;
  padding: 6px 12px;
  color: colors.c(text);
  font-size: 13px;

  &:hover {
    background: colors.c(background-alt, 0.4);
    border-color: colors.c(border, 0.25);
  }

  &:focus {
    border-color: colors.c(primary, 0.5);
    box-shadow: 0 0 0 2px colors.c(primary, 0.2);
  }

  selection {
    background: colors.c(primary, 0.3);
    color: colors.c(text);
  }
}

dropdown {
  background: colors.c(background-alt, 0.3);
  border: 1px solid colors.c(border, 0.15);
  border-radius: 8px;
  padding: 6px 12px;
  color: colors.c(text);
  font-size: 13px;
  min-width: 120px;

  &:hover {
    background: colors.c(background-alt, 0.4);
    border-color: colors.c(border, 0.25);
  }

  &:focus {
    border-color: colors.c(primary, 0.5);
    box-shadow: 0 0 0 2px colors.c(primary, 0.2);
  }

  button {
    background: transparent;
    border: none;
    padding: 0;

    arrow {
      color: colors.c(text, 0.6);
      margin-left: 4px;
    }
  }

  popover {
    background: colors.c(background-elevated, 0.95);
    backdrop-filter: blur(16px);
    border: 1px solid colors.c(border, 0.2);
    border-radius: 8px;
    box-shadow: 0 8px 24px colors.c(base, 0.4);
    padding: 4px;

    listview {
      background: transparent;

      row {
        border-radius: 6px;
        padding: 8px 12px;

        &:hover {
          background: colors.c(background-alt, 0.4);
        }

        &:selected {
          background: colors.c(primary, 0.2);
          color: colors.c(text);
        }
      }
    }
  }
}

// Keybind category headers
keybind-category {
  font-size: 13px;
  font-weight: 600;
  color: colors.c(primary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: 12px;
  margin-bottom: 4px;
}

// Keybind input component
keybind-input-wrapper {
  min-width: 150px;
}

keybind-input {
  background: colors.c(background-alt, 0.3);
  border: 1px solid colors.c(border, 0.15);
  border-radius: 8px;
  padding: 6px 12px;
  min-width: 120px;
  color: colors.c(text);
  font-size: 13px;
  font-family: monospace;
  transition: all 150ms ease;

  &:hover {
    background: colors.c(background-alt, 0.4);
    border-color: colors.c(border, 0.25);
  }

  &:focus {
    border-color: colors.c(primary, 0.5);
    box-shadow: 0 0 0 2px colors.c(primary, 0.2);
  }

  &.recording {
    background: colors.c(primary, 0.2);
    border-color: colors.c(primary, 0.6);
    animation: pulse 1s ease-in-out infinite;
  }

  label {
    font-size: 13px;
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0.7;
  }

  100% {
    opacity: 1;
  }
}

keybind-cancel,
keybind-clear {
  background: colors.c(background-alt, 0.3);
  border: 1px solid colors.c(border, 0.1);
  border-radius: 6px;
  padding: 4px 8px;
  min-width: 28px;
  min-height: 28px;
  color: colors.c(text, 0.7);
  transition: all 150ms ease;

  &:hover {
    background: colors.c(background-alt, 0.5);
    border-color: colors.c(border, 0.2);
    color: colors.c(text);
  }

  &:active {
    transform: scale(0.95);
  }

  label {
    font-size: 14px;
  }
}

keybind-info {
  background: colors.c(background-alt, 0.15);
  border: 1px solid colors.c(border, 0.08);
  border-radius: 8px;
  padding: 12px;
  margin-top: 16px;

  box {
    padding: 4px 0;

    label {
      font-size: 13px;
      color: colors.c(text, 0.8);
    }
  }
}

keybind-info-title {
  font-size: 13px;
  font-weight: 500;
  color: colors.c(text, 0.9);
  margin-bottom: 8px;
}

// Window management restart notice
window-restart-notice {
  background: colors.c(warning, 0.1);
  border: 1px solid colors.c(warning, 0.2);
  border-radius: 8px;
  padding: 12px;
  margin-top: 16px;
}

notice-text {
  font-size: 13px;
  color: colors.c(warning);
  font-weight: 500;
}

restart-button {
  background: colors.c(warning, 0.2);
  border: 1px solid colors.c(warning, 0.3);
  border-radius: 6px;
  padding: 6px 12px;
  margin-top: 8px;
  color: colors.c(text);
  font-size: 13px;
  font-weight: 500;
  transition: all 150ms ease;

  &:hover {
    background: colors.c(warning, 0.3);
    border-color: colors.c(warning, 0.4);
    box-shadow: 0 2px 8px colors.c(warning, 0.2);
  }

  &:active {
    transform: scale(0.95);
  }
}

// Theme Selector Styles
.theme-selector {
  padding: 0;
}

.section-header {
  margin-bottom: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: colors.c(text);
}

.theme-option {
  background: colors.c(background-alt, 0.2);
  border: 2px solid colors.c(border, 0.1);
  border-radius: 12px;
  padding: 16px;
  width: 100%;
  transition: all 200ms ease;

  &:hover {
    background: colors.c(background-alt, 0.3);
    border-color: colors.c(border, 0.2);
    box-shadow: 0 4px 12px colors.c(base, 0.2);
  }

  &:active {
    transform: scale(0.98);
  }

  &.active {
    background: colors.c(primary, 0.1);
    border-color: colors.c(primary, 0.5);
    box-shadow: 0 0 0 3px colors.c(primary, 0.1);
  }
}

.theme-name {
  font-size: 15px;
  font-weight: 600;
  color: colors.c(text);
}

.theme-description {
  font-size: 13px;
  color: colors.c(text, 0.7);
  margin-top: 2px;
}

.theme-active-icon {
  color: colors.c(primary);
}

.theme-preview {
  margin-top: 12px;
  padding: 8px;
  background: colors.c(background, 0.3);
  border-radius: 8px;
  justify-content: center;
}

.theme-color-swatch {
  width: 24px;
  height: 24px;
  border-radius: 6px;
  border: 1px solid colors.c(border, 0.2);

  // Rose Pine theme swatches
  &.theme-rose-pine-base {
    background: #191724;
  }

  &.theme-rose-pine-surface {
    background: #1f1d2e;
  }

  &.theme-rose-pine-overlay {
    background: #26233a;
  }

  &.theme-rose-pine-primary {
    background: #c4a7e7;
  }

  &.theme-rose-pine-accent {
    background: #eb6f92;
  }

  &.theme-rose-pine-text {
    background: #e0def4;
  }

  // Rose Pine Moon theme swatches
  &.theme-rose-pine-moon-base {
    background: #232136;
  }

  &.theme-rose-pine-moon-surface {
    background: #2a273f;
  }

  &.theme-rose-pine-moon-overlay {
    background: #393552;
  }

  &.theme-rose-pine-moon-primary {
    background: #c4a7e7;
  }

  &.theme-rose-pine-moon-accent {
    background: #eb6f92;
  }

  &.theme-rose-pine-moon-text {
    background: #e0def4;
  }

  // Rose Pine Dawn theme swatches
  &.theme-rose-pine-dawn-base {
    background: #faf4ed;
  }

  &.theme-rose-pine-dawn-surface {
    background: #fffaf3;
  }

  &.theme-rose-pine-dawn-overlay {
    background: #f2e9e1;
  }

  &.theme-rose-pine-dawn-primary {
    background: #907aa9;
  }

  &.theme-rose-pine-dawn-accent {
    background: #b4637a;
  }

  &.theme-rose-pine-dawn-text {
    background: #575279;
  }
}

.theme-hint {
  background: colors.c(background-alt, 0.15);
  border: 1px solid colors.c(border, 0.08);
  border-radius: 8px;
  padding: 12px;
  margin-top: 16px;
}

.hint-text {
  font-size: 13px;
  color: colors.c(text, 0.7);
}
