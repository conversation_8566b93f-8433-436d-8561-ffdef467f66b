@use "../../styles/colors";

// Fullscreen transparent window that contains the sidebar
sidebar-fullscreen {
  background-color: transparent;
  border: none;

  .left {
    margin: 0px;
    padding: 0px;
  }
}

// Debug: visualize click area
sidebar-click-area {
  // background-color: rgba(255, 0, 0, 0.1); // Light red tint for debugging
  background-color: transparent; // Transparent 
}

sidebar-container {
  background-color: transparent;
}

sidebar {
  background-color: colors.c(background-alt);
  border: 2px solid colors.c(border);
  border-radius: 24px;
  padding: 5px 10px;
  margin-top: 8px;
  margin-bottom: 18px;
  margin-left: 8px;
  margin-right: 8px;

  // drop shadow
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
  // Width constraints for better control
  min-width: 300px;
  max-width: 400px;

}

sidebar-right {}


sidebar-left {}



notification-list {
  margin-left: 20px;
  margin-right: 20px;
}


// Notification Styles
.notification-container {
  background: linear-gradient(135deg, colors.c(overlay, 0.3) 0%, colors.c(overlay, 0.1) 100%);
  border: 1px solid colors.c(border);
  border-radius: 16px;
  margin: 6px 0;
  transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    background: linear-gradient(135deg, colors.c(overlay, 0.5) 0%, colors.c(overlay, 0.3) 100%);
    border-color: colors.c(border-alt);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px colors.c(base, 0.3);
  }
}

.notification-clickable-area {
  padding: 16px;
  border-radius: 16px;

  &:active {
    transform: scale(0.99);
  }
}

.notification-icon-wrapper {
  min-width: 48px;
  min-height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, colors.c(overlay, 0.5) 0%, colors.c(overlay, 0.3) 100%);
  margin-right: 12px;

  &.urgency-low {
    background: linear-gradient(135deg, colors.c(success, 0.2) 0%, colors.c(success, 0.1) 100%);
  }

  &.urgency-normal {
    background: linear-gradient(135deg, colors.c(info, 0.2) 0%, colors.c(info, 0.1) 100%);
  }

  &.urgency-critical {
    background: linear-gradient(135deg, colors.c(error, 0.2) 0%, colors.c(error, 0.1) 100%);
  }
}

.notification-image-wrapper {
  min-width: 64px;
  min-height: 64px;
  border-radius: 12px;
  margin-right: 12px;
  background: colors.c(overlay, 0.3);

  &.urgency-critical {
    border: 2px solid colors.c(error, 0.3);
  }
}

.notification-image {
  border-radius: 10px;
}

.notification-content {}

.notification-header {
  margin-bottom: 4px;
}

.notification-title {
  font-weight: 600;
  font-size: 14px;
  color: colors.c(foreground);
  line-height: 1.3;
}

.notification-time {
  font-size: 12px;
  color: colors.c(foreground-muted);
  margin-left: 12px;
}

.notification-body {
  font-size: 13px;
  color: colors.c(foreground-alt);
  line-height: 1.4;
  margin-top: 4px;
}

.notification-body-expanded {
  font-size: 13px;
  color: colors.c(foreground-alt);
  line-height: 1.5;
  margin-top: 8px;
}

.notification-actions-container {
  padding: 0 16px 16px 16px;
}

.notification-actions {}

.notification-image-actions {
  margin-bottom: 8px;
}

.notification-action {
  padding: 6px 16px;
  border-radius: 8px;
  background: colors.c(overlay, 0.5);
  border: 1px solid colors.c(border);
  font-size: 13px;
  font-weight: 500;
  color: colors.c(foreground);
  transition: all 200ms ease;

  &:hover {
    background: colors.c(overlay, 0.7);
    border-color: colors.c(border-alt);
  }

  &:active {
    transform: scale(0.98);
  }

  &.image-action {
    background: colors.c(primary, 0.1);
    border-color: colors.c(primary, 0.2);

    &:hover {
      background: colors.c(primary, 0.15);
      border-color: colors.c(primary, 0.3);
    }
  }
}

.notification-expand-btn {
  min-width: 32px;
  min-height: 32px;
  border-radius: 8px;
  background: colors.c(overlay, 0.3);
  border: 1px solid colors.c(border);
  transition: all 200ms ease;
  margin-left: 8px;

  &:hover {
    background: colors.c(overlay, 0.5);
  }
}

.notification-list-controls {
  padding: 12px 0;
  border-top: 1px solid colors.c(border);
  margin-top: 12px;
}

.notification-count {
  font-size: 13px;
  color: colors.c(foreground-alt);
  font-weight: 500;
}

.notification-control-btn {
  padding: 6px 12px;
  border-radius: 8px;
  background: colors.c(overlay, 0.3);
  border: 1px solid colors.c(border);
  font-size: 13px;
  color: colors.c(foreground);
  transition: all 200ms ease;

  &:hover {
    background: colors.c(overlay, 0.5);
    border-color: colors.c(border-alt);
  }

  &.active {
    background: colors.c(primary, 0.2);
    border-color: colors.c(primary, 0.3);
    color: colors.c(primary);
  }
}

.notification-empty {
  color: colors.c(foreground-muted);
  min-height: 200px;

  .empty-icon {
    font-size: 48px;
    margin-bottom: 12px;
    opacity: 0.5;
  }

  .empty-text {
    font-size: 14px;
    font-weight: 500;
    opacity: 0.7;
  }
}

// WiFi Module Styles
wifi-module {
  padding: 16px;
}



wifi-status {
  background: linear-gradient(135deg, colors.c(background-elevated), colors.c(background));
  border-radius: 12px;
  padding: 16px;
  border: 1px solid colors.c(border);


  wifi-switch {
    margin-bottom: 24px;

    .wifi-active {
      background: colors.c(iris, 0.8);
      max-height: 12px;
    }

    .wifi-inactive {
      background: colors.c(pine, 0.8);
      max-height: 12px;
    }

  }



  wifi-icon-wrapper {
    min-width: 48px;
    min-height: 48px;
    background: colors.c(primary, 0.1);
    border-radius: 12px;

    wifi-icon {
      padding-left: 12px;

    }

    image {
      color: colors.c(primary);
    }
  }

  wifi-status-title {
    font-size: 16px;
    font-weight: 600;
    color: colors.c(foreground);
  }

  wifi-status-subtitle {
    font-size: 14px;
    color: colors.c(foreground-alt);
    margin-top: 2px;
  }

  switch {
    margin: 0;
  }

  wifi-info {
    margin-top: 12px;
    padding: 8px 12px;
    background: colors.c(background, 0.5);
    border-radius: 8px;

    image {
      color: colors.c(foreground-alt);
    }

    label {
      font-size: 12px;
      color: colors.c(foreground-alt);
    }
  }
}

wifi-list {
  background: colors.c(background-elevated);
  border-radius: 12px;
  padding: 12px;
  border: 1px solid colors.c(border);

  wifi-list-header {
    margin-bottom: 12px;
    padding: 0 4px;

    label {
      font-size: 14px;
      font-weight: 500;
      color: colors.c(foreground);
    }
  }

  wifi-refresh-btn {
    padding: 6px;
    background: colors.c(background, 0.5);
    border-radius: 8px;
    border: none;

    &:hover {
      background: colors.c(background, 0.8);
    }

    image {
      color: colors.c(foreground-alt);
    }

    .spinning {
      animation: spin 1s linear infinite;
    }
  }

  wifi-list-scroll {
    background: transparent;
  }

  wifi-empty {
    padding: 48px 24px;

    image {
      color: colors.c(foreground-alt, 0.3);
    }

    label {
      font-size: 14px;
      color: colors.c(foreground-alt);
    }
  }
}

wifi-item {
  background: colors.c(background, 0.5);
  border: 1px solid transparent;
  border-radius: 10px;
  padding: 12px;
  margin-bottom: 8px;
  transition: all 0.2s ease;

  &:hover {
    background: colors.c(background, 0.8);
    border-color: colors.c(border);
    transform: translateY(-1px);
  }

  &.connected {
    background: colors.c(primary, 0.1);
    border-color: colors.c(primary, 0.3);

    &:hover {
      background: colors.c(primary, 0.15);
      border-color: colors.c(primary, 0.4);
    }

    wifi-item-status {
      color: colors.c(primary);
    }
  }

  wifi-item-icon {
    min-width: 32px;

    image {
      color: colors.c(foreground);
    }

    .wifi-secure-icon {
      background: colors.c(background-elevated);
      border-radius: 50%;
      padding: 2px;
      color: colors.c(foreground-alt);
    }
  }

  wifi-item-name {
    font-size: 14px;
    font-weight: 500;
    color: colors.c(foreground);
  }

  wifi-item-status {
    font-size: 12px;
    color: colors.c(foreground-alt);
    margin-top: 2px;
  }

  wifi-strength {
    font-size: 12px;
    color: colors.c(foreground-alt);
    min-width: 35px;
  }

  wifi-connecting {
    margin-left: 8px;
    color: colors.c(primary);
  }

  &.connecting {
    background: colors.c(primary, 0.05);
    border-color: colors.c(primary, 0.2);
  }

  &.failed {
    background: colors.c(error, 0.05);
    border-color: colors.c(error, 0.2);

    wifi-item-status {
      color: colors.c(error);
    }
  }

  .wifi-success {
    color: colors.c(success);
  }

  .wifi-error {
    color: colors.c(error);
  }
}

// WiFi password dialog
wifi-password-dialog {
  background: colors.c(background-elevated);
  border: 1px solid colors.c(border);
  border-radius: 10px;
  padding: 16px;
  margin: 8px 12px;
  box-shadow: 0 4px 12px colors.c(background, 0.4);

  wifi-password-header {
    margin-bottom: 12px;

    image {
      color: colors.c(primary);
    }

    label {
      font-size: 16px;
      font-weight: 500;
      color: colors.c(foreground);
    }
  }

  wifi-password-label {
    font-size: 13px;
    color: colors.c(foreground-alt);
    margin-bottom: 4px;
  }

  wifi-password-entry {
    background: colors.c(background, 0.5);
    border: 1px solid colors.c(border, 0.5);
    border-radius: 8px;
    padding: 8px 12px;
    color: colors.c(foreground);
    font-size: 14px;

    &:focus {
      border-color: colors.c(primary, 0.5);
      box-shadow: 0 0 0 2px colors.c(primary, 0.1);
    }
  }

  wifi-password-toggle {
    padding: 8px;
    background: colors.c(background, 0.3);
    border: 1px solid colors.c(border, 0.3);
    border-radius: 8px;

    &:hover {
      background: colors.c(background, 0.5);
      border-color: colors.c(border, 0.5);
    }

    image {
      color: colors.c(foreground-alt);
    }
  }

  wifi-password-cancel,
  wifi-password-connect {
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.2s ease;
  }

  wifi-password-cancel {
    background: colors.c(background, 0.3);
    border: 1px solid colors.c(border, 0.3);
    color: colors.c(foreground-alt);

    &:hover {
      background: colors.c(background, 0.5);
      border-color: colors.c(border, 0.5);
      color: colors.c(foreground);
    }
  }

  wifi-password-connect {
    background: colors.c(primary, 0.8);
    border: 1px solid colors.c(primary);
    color: colors.c(background);
    margin-left: 8px;

    &:hover {
      background: colors.c(primary, 0.9);
    }

    &:insensitive {
      opacity: 0.5;
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

// Bluetooth Module Styles
bluetooth-module {
  padding: 16px;
}

bluetooth-status {
  background: linear-gradient(135deg, colors.c(background-elevated), colors.c(background));
  border-radius: 12px;
  padding: 16px;
  border: 1px solid colors.c(border);

  bluetooth-icon-wrapper {
    min-width: 48px;
    min-height: 48px;
    background: colors.c(primary, 0.1);
    border-radius: 12px;

    bluetooth-icon {
      padding: 0;
    }

    image {
      color: colors.c(primary);
    }
  }

  bluetooth-status-title {
    font-size: 16px;
    font-weight: 600;
    color: colors.c(foreground);
  }

  bluetooth-status-subtitle {
    font-size: 14px;
    color: colors.c(foreground-alt);
    margin-top: 2px;
  }

  bluetooth-switch {
    switch {
      margin: 0;
    }
  }
}

bluetooth-list {
  background: colors.c(background-elevated);
  border-radius: 12px;
  padding: 12px;
  border: 1px solid colors.c(border);

  bluetooth-list-header {
    margin-bottom: 12px;
    padding: 0 4px;

    label {
      font-size: 14px;
      font-weight: 500;
      color: colors.c(foreground);
    }
  }

  bluetooth-filter-btn,
  bluetooth-scan-btn {
    padding: 6px;
    background: colors.c(background, 0.5);
    border-radius: 8px;
    border: none;
    margin-left: 4px;

    &:hover {
      background: colors.c(background, 0.8);
    }

    image {
      color: colors.c(foreground-alt);
    }

    &.scanning {
      background: colors.c(primary, 0.2);

      image {
        color: colors.c(primary);
      }
    }

    .spinning {
      animation: spin 1s linear infinite;
    }
  }

  bluetooth-list-scroll {
    background: transparent;
  }

  bluetooth-empty {
    padding: 48px 24px;

    image {
      color: colors.c(foreground-alt, 0.3);
    }

    label {
      font-size: 14px;
      color: colors.c(foreground-alt);
    }

    bluetooth-hint {
      font-size: 12px;
      color: colors.c(foreground-alt, 0.6);
      margin-top: 8px;
    }
  }
}

bluetooth-item {
  margin-bottom: 8px;
}

bluetooth-item-button {
  background: colors.c(background, 0.5);
  border: 1px solid transparent;
  border-radius: 10px;
  padding: 12px;
  transition: all 0.2s ease;

  &:hover {
    background: colors.c(background, 0.8);
    border-color: colors.c(border);
    transform: translateY(-1px);
  }

  &.connected {
    background: colors.c(primary, 0.1);
    border-color: colors.c(primary, 0.3);

    &:hover {
      background: colors.c(primary, 0.15);
      border-color: colors.c(primary, 0.4);
    }

    bluetooth-item-status {
      color: colors.c(primary);
    }
  }

  &.paired:not(.connected) {
    border-color: colors.c(border, 0.5);
  }

  bluetooth-item-icon {
    min-width: 32px;

    image {
      color: colors.c(foreground);
    }
  }

  bluetooth-item-name {
    font-size: 14px;
    font-weight: 500;
    color: colors.c(foreground);
  }

  bluetooth-item-info {
    margin-top: 2px;

    box {

      image {
        color: colors.c(foreground-alt, 0.6);
      }

      label {
        font-size: 11px;
        color: colors.c(foreground-alt, 0.6);
      }
    }
  }

  bluetooth-item-status {
    font-size: 12px;
    color: colors.c(foreground-alt);
    font-weight: 500;
  }

  bluetooth-connecting,
  bluetooth-removing {
    margin-left: 8px;
  }
}

bluetooth-remove-btn {
  padding: 8px;
  background: colors.c(background, 0.3);
  border: 1px solid transparent;
  border-radius: 8px;
  transition: all 0.2s ease;

  &:hover {
    background: colors.c(error, 0.1);
    border-color: colors.c(error, 0.2);

    image {
      color: colors.c(error);
    }
  }

  &:active {
    background: colors.c(error, 0.15);
  }

  image {
    color: colors.c(foreground-alt);
  }
}

bluetooth-scanning-info {
  padding: 12px;
  margin: 8px 0;
  background: colors.c(primary, 0.1);
  border: 1px solid colors.c(primary, 0.2);
  border-radius: 8px;

  bluetooth-scan-spinner {
    color: colors.c(primary);
  }

  label {
    font-size: 13px;
    color: colors.c(primary);
  }
}

// Audio Module Styles
audio-module {
  padding: 0;
  min-height: 400px;

  scrolledwindow {
    background: transparent;
  }
}

audio-module-content {
  padding: 16px;
}

// AI Settings Styles
ai-settings-container {
  background: transparent;

  scrollbar {
    opacity: 0.3;
    transition: opacity 0.2s ease;

    &:hover {
      opacity: 0.6;
    }
  }
}

ai-settings-content {
  padding: 20px;
}

settings-title {
  font-size: 24px;
  font-weight: 600;
  color: colors.c(foreground);
  margin-bottom: 8px;
}

settings-section {
  background: colors.c(background-elevated, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid colors.c(border, 0.3);
  border-radius: 12px;
  padding: 16px;
  transition: all 0.3s ease;

  &:hover {
    background: colors.c(background-elevated, 0.4);
    border-color: colors.c(border, 0.4);
    box-shadow: 0 4px 12px colors.c(background, 0.2);
  }
}

settings-section-header {
  background: transparent;
  padding: 12px 16px;
  margin: -16px -16px 0 -16px;
  border-radius: 12px;
  transition: all 0.2s ease;

  &:hover {
    background: colors.c(primary, 0.05);
  }

  &:active {
    background: colors.c(primary, 0.1);
  }

  box {
    image {
      color: colors.c(primary);
      transition: transform 0.2s ease;
    }

    label {
      font-size: 18px;
      font-weight: 500;
      color: colors.c(foreground);
    }
  }
}

settings-section-content {
  padding: 0 16px;
}

// AI Provider Settings
ai-provider-settings {
  background: colors.c(background, 0.2);
  backdrop-filter: blur(8px);
  border: 1px solid colors.c(border, 0.2);
  border-radius: 10px;
  padding: 16px;
  transition: all 0.2s ease;

  &:hover {
    background: colors.c(background, 0.3);
    border-color: colors.c(primary, 0.3);
  }
}

ai-provider-header {
  margin-bottom: 12px;

  image {
    color: colors.c(primary);
  }
}

ai-provider-name {
  font-size: 16px;
  font-weight: 600;
  color: colors.c(foreground);
}

// Input Components
settings-input-label {
  font-size: 12px;
  font-weight: 500;
  color: colors.c(foreground-alt);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

settings-input {
  background: colors.c(background, 0.3);
  border: 1px solid colors.c(border, 0.3);
  border-radius: 8px;
  padding: 10px 14px;
  color: colors.c(foreground);
  font-size: 14px;
  transition: all 0.2s ease;

  &:hover {
    background: colors.c(background, 0.4);
    border-color: colors.c(border, 0.5);
  }

  &:focus {
    background: colors.c(background, 0.5);
    border-color: colors.c(primary, 0.5);
    box-shadow: 0 0 0 3px colors.c(primary, 0.1);
  }
}

// Slider Components
settings-slider {
  padding: 0;

  trough {
    background: colors.c(background, 0.5);
    border-radius: 4px;
    min-height: 6px;
  }

  highlight {
    background: linear-gradient(90deg, colors.c(primary), colors.c(secondary));
    border-radius: 4px;
  }

  slider {
    background: colors.c(foreground);
    border-radius: 50%;
    min-width: 16px;
    min-height: 16px;
    margin: -5px 0;
    box-shadow: 0 2px 4px colors.c(background, 0.4);
    transition: all 0.2s ease;

    &:hover {
      background: colors.c(primary);
      box-shadow: 0 2px 8px colors.c(primary, 0.3);
    }
  }
}

settings-slider-value {
  font-size: 12px;
  font-weight: 600;
  color: colors.c(primary);
  min-width: 30px;
}

// Dropdown Components
settings-dropdown {
  background: colors.c(background, 0.3);
  border: 1px solid colors.c(border, 0.3);
  border-radius: 8px;
  padding: 10px 14px;
  transition: all 0.2s ease;

  &:hover {
    background: colors.c(background, 0.4);
    border-color: colors.c(border, 0.5);
  }

  label {
    color: colors.c(foreground);
    font-size: 14px;
  }

  image {
    color: colors.c(foreground-alt);
  }
}

settings-dropdown-menu {
  margin-top: 4px;
  background: colors.c(background-elevated, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid colors.c(border, 0.4);
  border-radius: 8px;
  padding: 4px;
  box-shadow: 0 4px 12px colors.c(background, 0.3);
}

settings-dropdown-item {
  background: transparent;
  border-radius: 6px;
  padding: 8px 12px;
  transition: all 0.15s ease;

  &:hover {
    background: colors.c(primary, 0.1);
  }

  &.active {
    background: colors.c(primary, 0.2);

    label {
      color: colors.c(primary);
      font-weight: 500;
    }
  }

  label {
    font-size: 14px;
    color: colors.c(foreground);
  }
}

// Custom Toggle Button
settings-toggle {
  background: colors.c(background, 0.5);
  border: 1px solid colors.c(border, 0.3);
  border-radius: 12px;
  min-width: 44px;
  min-height: 24px;
  padding: 2px;
  transition: all 0.2s ease;

  &:hover {
    background: colors.c(background, 0.6);
    border-color: colors.c(border, 0.4);
  }

  &.active {
    background: linear-gradient(90deg, colors.c(primary, 0.8), colors.c(secondary, 0.8));
    border-color: transparent;

    .toggle-indicator {
      margin-left: 20px;
      background: colors.c(foreground);
    }
  }

  .toggle-indicator {
    background: colors.c(foreground-alt);
    border-radius: 50%;
    min-width: 18px;
    min-height: 18px;
    margin-left: 0;
    transition: all 0.2s ease;
  }
}

// Number Input
settings-number-input {
  background: colors.c(background, 0.3);
  border: 1px solid colors.c(border, 0.3);
  border-radius: 8px;
  padding: 6px 10px;
  color: colors.c(foreground);
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  min-width: 80px;

  &:hover {
    background: colors.c(background, 0.4);
    border-color: colors.c(border, 0.5);
  }

  &:focus {
    background: colors.c(background, 0.5);
    border-color: colors.c(primary, 0.5);
    box-shadow: 0 0 0 3px colors.c(primary, 0.1);
  }
}

// Button Variants
settings-button-secondary {
  background: colors.c(primary, 0.1);
  border: 1px solid colors.c(primary, 0.3);
  border-radius: 8px;
  padding: 10px 16px;
  transition: all 0.2s ease;

  &:hover {
    background: colors.c(primary, 0.2);
    border-color: colors.c(primary, 0.5);
    box-shadow: 0 2px 8px colors.c(primary, 0.2);
  }

  &:active {
    background: colors.c(primary, 0.3);
  }

  label {
    font-size: 14px;
    font-weight: 500;
    color: colors.c(primary);
  }

  image {
    color: colors.c(primary);
  }
}

settings-button-danger {
  background: colors.c(error, 0.1);
  border: 1px solid colors.c(error, 0.3);
  border-radius: 8px;
  padding: 10px 16px;
  transition: all 0.2s ease;

  &:hover {
    background: colors.c(error, 0.2);
    border-color: colors.c(error, 0.5);
    box-shadow: 0 2px 8px colors.c(error, 0.2);
  }

  &:active {
    background: colors.c(error, 0.3);
  }

  label {
    font-size: 14px;
    font-weight: 500;
    color: colors.c(error);
  }

  image {
    color: colors.c(error);
  }
}

// Status Indicators
.icon-success {
  color: colors.c(success);
}

.text-success {
  font-size: 12px;
  font-weight: 500;
  color: colors.c(success);
}

// Subsection titles
settings-subsection-title {
  font-size: 14px;
  font-weight: 600;
  color: colors.c(foreground);
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  opacity: 0.8;
}

// Empty state
settings-empty-state {
  font-size: 13px;
  color: colors.c(foreground-alt, 0.5);
  font-style: italic;
  padding: 20px;
}

// MCP Server Item
mcp-server-item {
  background: colors.c(background, 0.2);
  border: 1px solid colors.c(border, 0.2);
  border-radius: 8px;
  padding: 12px;
  transition: all 0.2s ease;

  &:hover {
    background: colors.c(background, 0.3);
    border-color: colors.c(border, 0.3);
  }
}

mcp-server-name {
  font-size: 14px;
  font-weight: 500;
  color: colors.c(foreground);
}

mcp-server-url {
  font-size: 12px;
  color: colors.c(foreground-alt, 0.7);
  margin-top: 2px;
}

// Small toggle for list items
settings-toggle-small {
  background: colors.c(background, 0.5);
  border: 1px solid colors.c(border, 0.3);
  border-radius: 10px;
  min-width: 36px;
  min-height: 20px;
  padding: 2px;
  transition: all 0.2s ease;

  &:hover {
    background: colors.c(background, 0.6);
    border-color: colors.c(border, 0.4);
  }

  &.active {
    background: linear-gradient(90deg, colors.c(primary, 0.8), colors.c(secondary, 0.8));
    border-color: transparent;

    .toggle-indicator-small {
      margin-left: 16px;
      background: colors.c(foreground);
    }
  }

  .toggle-indicator-small {
    background: colors.c(foreground-alt);
    border-radius: 50%;
    min-width: 14px;
    min-height: 14px;
    margin-left: 0;
    transition: all 0.2s ease;
  }
}

// Icon button
settings-button-icon {
  background: transparent;
  border-radius: 6px;
  padding: 6px;
  transition: all 0.2s ease;

  &:hover {
    background: colors.c(error, 0.1);

    image {
      color: colors.c(error);
    }
  }

  &:active {
    background: colors.c(error, 0.15);
  }

  image {
    color: colors.c(foreground-alt);
  }
}

audio-devices-container {}

audio-devices-column {}

audio-section-title {
  font-size: 14px;
  font-weight: 600;
  color: colors.c(foreground);
  margin-bottom: 4px;
}

audio-section-header {
  margin-bottom: 8px;
}

audio-filter-btn {
  padding: 4px;
  background: colors.c(background, 0.3);
  border-radius: 6px;
  border: 1px solid transparent;

  &:hover {
    background: colors.c(background, 0.5);
    border-color: colors.c(border);
  }

  image {
    color: colors.c(foreground-alt);
  }
}

// Device Selection
audio-device-item {
  background: colors.c(background, 0.3);
  border-radius: 10px;
  padding: 10px;
  border: 1px solid colors.c(border, 0.5);
  transition: all 0.2s ease;
}

audio-device-select {
  background: transparent;
  border: none;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s ease;

  &:hover {
    background: colors.c(background, 0.5);
  }

  &.default {
    background: colors.c(primary, 0.1);

    &:hover {
      background: colors.c(primary, 0.15);
    }
  }
}

audio-device-icon-wrapper {
  min-width: 36px;
  min-height: 36px;
  background: colors.c(background-elevated);
  border-radius: 8px;
  padding: 0;

  image {
    color: colors.c(foreground-alt);
    margin: 0;
    padding: 0;
  }
}

audio-device-name {
  font-size: 14px;
  font-weight: 500;
  color: colors.c(foreground);
}

audio-device-status {
  font-size: 12px;
  color: colors.c(primary);
  margin-top: 2px;
}

audio-device-check {
  color: colors.c(primary);
}

// Volume Controls
volume-control {
  padding: 0 4px;
}

volume-mute-btn {
  padding: 6px;
  background: colors.c(background, 0.3);
  border: 1px solid transparent;
  border-radius: 6px;
  transition: all 0.2s ease;

  &:hover {
    background: colors.c(background, 0.5);
    border-color: colors.c(border);
  }

  image {
    color: colors.c(foreground-alt);
  }
}

volume-slider-container {
  margin-top: 8px;
  margin-bottom: 8px;
}

volume-slider {
  height: 6px;

  slider {
    min-height: 6px;
    border-radius: 3px;
    background-color: colors.c(muted, 0.5);
  }

  trough {
    min-height: 6px;
    border-radius: 3px;
    background: colors.c(muted, 0.5);
  }

  highlight {
    min-height: 6px;
    border-radius: 3px;
    background: colors.c(rose, 0.7);
  }
}

volume-label {
  font-size: 12px;
  color: colors.c(foreground-alt);
  font-variant-numeric: tabular-nums;
}

// App Streams
audio-streams-scroll {
  background: colors.c(background-elevated);
  border-radius: 10px;
  border: 1px solid colors.c(border);
}

audio-stream-item {
  background: colors.c(background, 0.3);
  border-radius: 8px;
  padding: 10px;
  margin: 4px 8px;
  border: 1px solid transparent;
  transition: all 0.2s ease;

  &:hover {
    background: colors.c(background, 0.5);
    border-color: colors.c(border);
  }
}

audio-stream-icon-wrapper {
  min-width: 32px;
  min-height: 32px;
  background: colors.c(background-elevated);
  border-radius: 8px;
  padding: 0;

  image {
    color: colors.c(foreground-alt);
    margin: 0;
    padding: 0;
  }
}

audio-stream-name {
  font-size: 13px;
  font-weight: 500;
  color: colors.c(foreground);
}

audio-no-devices,
audio-no-streams {
  padding: 24px;

  image {
    color: colors.c(foreground-alt, 0.3);
  }

  label {
    font-size: 13px;
    color: colors.c(foreground-alt);
  }
}

audio-error {
  padding: 48px;

  image {
    color: colors.c(error, 0.5);
  }

  label {
    font-size: 14px;
    color: colors.c(foreground-alt);
  }
}

// Header Module Styles
header-module {
  margin: 16px;
  padding: 16px;
  background: colors.c(background-elevated);
  border-radius: 12px;
  border: 1px solid colors.c(border);

  header-info {
    distro-icon-wrapper {
      min-width: 42px;
      min-height: 42px;
      background: colors.c(primary, 0.1);
      border-radius: 12px;

      image {
        color: colors.c(primary);
      }
    }

    user-avatar-wrapper {
      min-width: 48px;
      min-height: 48px;
      margin-right: 4px;
      border-radius: 50%;
      border: 2px solid colors.c(border-alt);

      .user-avatar {
        border-radius: 50%;
        border: 2px solid colors.c(border);
      }

      .user-avatar-placeholder {
        background: colors.c(primary, 0.1);
        border-radius: 50%;
        border: 2px solid colors.c(border);

        image {
          color: colors.c(primary);
        }
      }
    }

    header-title {
      font-size: 16px;
      font-weight: 600;
      color: colors.c(foreground);
    }

    header-uptime {
      font-size: 12px;
      color: colors.c(foreground-alt);
      margin-top: 2px;
    }
  }

  header-actions {}

  header-button {
    min-width: 24px;
    min-height: 24px;
    padding: 6px;
    background: colors.c(background, 0.5);
    border: 1px solid transparent;
    border-radius: 50%;
    transition: all 0.2s ease;

    image {
      color: colors.c(foreground-alt);
    }

    &:hover {
      background: colors.c(background, 0.8);
      border-color: colors.c(border);
      transform: scale(1.1);

      image {
        color: colors.c(foreground);
      }
    }

    &:active {
      background: colors.c(background);
    }

  }
}


power-button {

  image {
    color: colors.c(error);
  }

  &:hover {
    transform: scale(1.1);
    // glow
  }

  &:active {}

}

// Toggle Indicator Styles
.indicator-icon {
  color: colors.c(foreground-alt);

  &.active {
    color: colors.c(primary);
  }
}

// Quick Toggles Module
quick-toggles {
  padding: 16px;
  background: colors.c(background-elevated);
  border-radius: 12px;
  margin: 0 16px;
  border: 1px solid colors.c(border);
}

toggle-container {
  min-width: 80px;
}

toggle-button {
  min-width: 80px;
  min-height: 64px;
  padding: 0 16px;
  background: colors.c(background, 0.5);
  border: 1px solid colors.c(border);
  border-radius: 16px;
  transition: all 0.2s ease;

  &:hover {
    background: colors.c(background, 0.7);
    border-color: colors.c(border-alt);
  }

  &.active {
    background: colors.c(primary, 0.1);
    border-color: colors.c(primary, 0.3);

    &:hover {
      background: colors.c(primary, 0.15);
      border-color: colors.c(primary, 0.4);
    }
  }
}

toggle-button-content {}

toggle-icon-wrapper {
  padding: 0;
  min-width: 20px;

  .indicator-icon {
    color: colors.c(foreground-alt);
    transition: color 0.2s ease;
  }

  &.active .indicator-icon {
    color: colors.c(primary);
  }
}

toggle-label {
  font-size: 14px;
  font-weight: 500;
  color: colors.c(foreground-alt);
  transition: color 0.2s ease;

  &.active {
    color: colors.c(foreground);
  }
}

// Modern toggle switch styling
toggle-switch-container {
  margin-top: 2px;
  padding: 2px 0;
  background: colors.c(muted);
  border-radius: 12px;
  width: 20px;
  height: 20px;

  &.active {
    background: colors.c(secondary);
  }
}

toggle-switch {
  min-width: 40px;
  min-height: 2px;
  background: colors.c(background, 0.3);
  border-radius: 12px;
  border: 1px solid colors.c(border);
  padding: 2px;
  transition: all 0.3s ease;

  &:hover {
    background: colors.c(background, 0.5);
  }

  &.active {
    background: colors.c(primary, 0.8);
    border-color: colors.c(primary);

    &:hover {
      background: colors.c(primary, 0.9);
    }
  }
}

toggle-switch-thumb {
  min-width: 18px;
  min-height: 18px;
  background: colors.c(foreground);
  border-radius: 50%;
  transition: all 0.3s ease;

  &.active {
    margin-left: 18px;
    background: white;
  }
}

// Tab Container Styles
tab-container {
  margin-top: 16px;
}

tab-header-wrapper {
  background: colors.c(background-elevated);
  border-radius: 12px;
  border: 1px solid colors.c(border);
  padding: 4px;
  margin: 0 8px 8px 8px;
}

tab-header {
  background: colors.c(background, 0.3);
  border-radius: 10px;
  padding: 2px;
}

tab-btn {
  min-height: 44px;
  background: transparent;
  border: 1px solid transparent;
  border-radius: 8px;
  transition: all 0.2s ease;

  &:hover:not(.active) {
    background: colors.c(background, 0.5);
    border-color: colors.c(border);
  }

  &.active {
    background: colors.c(accent, 0.15);
    border: 1px solid colors.c(accent, 0.3);
    box-shadow: 0 2px 8px colors.c(accent, 0.2);

    tab-icon {
      color: colors.c(primary);
    }

    tab-label {
      color: colors.c(primary);
      font-weight: 600;
    }

    &::after {
      content: '';
      min-height: 2px;
      background: colors.c(primary);
      border-radius: 2px 2px 0 0;
    }
  }
}

tab-btn-content {
  padding: 8px 5px;
}

tab-icon-wrapper {}

tab-icon {
  color: colors.c(foreground-alt);
  transition: color 0.2s ease;
}

tab-label {
  font-size: 13px;
  color: colors.c(foreground-alt);
  transition: all 0.2s ease;
}

tab-content-wrapper {
  padding: 0 4px;

  // Give more space for notifications
  [name="notifications"] & {
    // padding: 0 4px;
  }
}

// Tools Module Styles
tools-module {
  padding: 16px;
}

tools-search-container {
  background: colors.c(background-elevated);
  border-radius: 12px;
  padding: 12px;
  border: 1px solid colors.c(border);
}

tools-search {
  background: colors.c(background, 0.5);
  border: 1px solid colors.c(border);
  border-radius: 8px;
  padding: 8px 12px;
  color: colors.c(foreground);
  font-size: 14px;

  &:focus {
    border-color: colors.c(primary, 0.5);
    background: colors.c(background, 0.7);
  }
}

tools-categories {
  padding: 0 4px;
  margin-top: 12px;
  margin-bottom: 12px;
}

tools-category-btn {
  background: colors.c(background, 0.3);
  border: 1px solid colors.c(border);
  border-radius: 8px;
  padding: 6px 12px;
  transition: all 0.2s ease;

  &:hover {
    background: colors.c(background, 0.5);
    border-color: colors.c(border-alt);
  }

  &.active {
    background: colors.c(primary, 0.15);
    border-color: colors.c(primary, 0.3);

    tools-category-icon {
      color: colors.c(primary);
    }

    label {
      color: colors.c(primary);
      font-weight: 600;
    }
  }
}

tools-category-icon {
  color: colors.c(foreground-alt);
  min-width: 16px;
}


tools-scroll {
  background: transparent;
}

tools-grid {
  padding: 4px;
}

tool-item {
  background: colors.c(background, 0.3);
  border: 1px solid colors.c(border);
  border-radius: 12px;
  padding: 16px;
  transition: all 0.2s ease;

  &:hover {
    background: colors.c(background, 0.5);
    border-color: colors.c(primary, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px colors.c(base, 0.2);

    tool-arrow {
      color: colors.c(primary);
      transform: translateX(4px);
    }
  }

  &:active {
    transform: translateY(0);
  }
}

tool-icon-wrapper {
  min-width: 48px;
  min-height: 48px;
  background: colors.c(primary, 0.1);
  border-radius: 12px;
  padding: 0;

  tool-icon {
    color: colors.c(primary);
  }
}

tool-name {
  font-size: 15px;
  font-weight: 600;
  color: colors.c(foreground);
  margin-bottom: 4px;
}

tool-description {
  font-size: 13px;
  color: colors.c(foreground-alt);
  line-height: 1.4;
}

tool-arrow {
  color: colors.c(foreground-alt, 0.5);
  transition: all 0.2s ease;
}

tools-empty {
  padding: 48px;

  .empty-icon {
    font-size: 48px;
    color: colors.c(foreground-alt, 0.3);
    margin-bottom: 12px;
  }

  .empty-text {
    font-size: 14px;
    color: colors.c(foreground-alt);
  }
}

// Tool expanded content styles
tool-container {
  background: transparent;
  margin-bottom: 8px;

  &:last-child {
    margin-bottom: 0;
  }
}

tool-expanded {
  padding: 16px;
  margin-top: -1px;
  background: colors.c(background-elevated, 0.5);
  border: 1px solid colors.c(border);
  border-top: none;
  border-radius: 0 0 12px 12px;
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

tool-input-container {
  background: colors.c(background, 0.3);
  border-radius: 8px;
  padding: 8px;
  border: 1px solid colors.c(border);
}

tool-input {
  background: transparent;
  border: none;
  color: colors.c(foreground);
  font-size: 14px;
  padding: 4px 8px;

  &:focus {
    outline: none;
  }
}

tool-execute-btn {
  padding: 8px 12px;
  background: colors.c(primary, 0.15);
  border: 1px solid colors.c(primary, 0.3);
  border-radius: 6px;
  transition: all 0.2s ease;

  &:hover {
    background: colors.c(primary, 0.2);
    border-color: colors.c(primary, 0.4);
  }

  &:active {
    background: colors.c(primary, 0.25);
  }

  PhosphorIcon {
    color: colors.c(primary);
  }
}

tool-loading {
  padding: 12px;

  spinner {
    color: colors.c(primary);
  }

  label {
    color: colors.c(foreground-alt);
    font-size: 13px;
  }
}

tool-result {
  background: colors.c(base, 0.3);
  border: 1px solid colors.c(border);
  border-radius: 8px;
  padding: 12px;
}

tool-result-text {
  font-family: monospace;
  font-size: 13px;
  color: colors.c(foreground);
  line-height: 1.5;
}

tool-error {
  background: colors.c(error, 0.1);
  border: 1px solid colors.c(error, 0.2);
  border-radius: 8px;
  padding: 12px;
}

tool-error-text {
  font-size: 13px;
  color: colors.c(error);
  line-height: 1.4;
}

// AI Chat Module Styles
ai-module {}

ai-chat-container {
  background: colors.c(background-elevated);
  border-radius: 12px;
  padding: 8px;
  border: 1px solid colors.c(border);
  margin-bottom: 16px;
}

// Spacing utilities
.spacing-v-5 {
  >* {
    margin-bottom: 5px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.spacing-v-10 {
  >* {
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.spacing-v-15 {
  >* {
    margin-bottom: 15px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.width-10 {
  min-width: 10px;
}

// Welcome message
.ai-welcome-message {
  text-align: center;
  padding: 48px 24px;

  label {
    font-size: 18px;
    font-weight: 600;
    color: colors.c(foreground-alt);
    opacity: 0.6;
  }
}

// Command helper tooltip
.command-helper {
  position: absolute;
  bottom: 100%;
  left: 0;
  right: 0;
  background: colors.c(background-elevated, 0.95);
  border: 1px solid colors.c(border);
  border-radius: 12px;
  padding: 12px;
  margin-bottom: 8px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px colors.c(base, 0.2);

  .command-item {
    padding: 4px 0;

    .command-name {
      font-family: monospace;
      color: colors.c(primary);
      font-weight: 600;
    }

    .command-desc {
      color: colors.c(foreground-alt);
      font-size: 12px;
      margin-left: 8px;
    }
  }
}

// Chat view container
sidebar-chat-wrapper {
  background: transparent;
  border-radius: 12px;

  scrollbar {
    width: 6px;
    background: transparent;

    slider {
      background: colors.c(foreground-alt, 0.3);
      border-radius: 3px;
      min-width: 6px;

      &:hover {
        background: colors.c(foreground-alt, 0.5);
      }
    }
  }
}

// Chat messages container
sidebar-chat-message {
  margin: 6px 8px;
  padding: 10px;
  background: colors.c(background-elevated, 0.3);
  border: 1px solid colors.c(border, 0.3);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  animation: messageSlideIn 0.3s ease-out;

  &:hover {
    background: colors.c(background-elevated, 0.4);
    border-color: colors.c(border, 0.4);
  }
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Chat user/bot names
sidebar-chat-name {
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 8px;

  &.user {
    color: colors.c(primary);
  }

  &.bot {
    color: colors.c(accent);
  }
}

// Message content area
sidebar-chat-messagearea {
  padding: 0;
}

sidebar-chat-message-content {
  padding: 0;
}

// Text blocks in messages
sidebar-chat-txtblock {
  font-size: 14px;
  color: colors.c(foreground);
  line-height: 1.6;

  &.sidebar-chat-txt {
    selection {
      background-color: colors.c(primary, 0.3);
      color: colors.c(foreground);
    }
  }
}

// Code blocks
sidebar-chat-codeblock {
  background: colors.c(base, 0.5);
  border: 1px solid colors.c(border);
  border-radius: 8px;
  margin: 8px 0;
  overflow: hidden;
}

sidebar-chat-codeblock-header {
  background: colors.c(background-elevated, 0.5);
  border-bottom: 1px solid colors.c(border);
  padding: 8px 12px;

  label {
    font-size: 12px;
    color: colors.c(foreground-alt);
    font-family: monospace;
  }
}

sidebar-chat-codeblock-content {
  padding: 12px;

  textview {
    background: transparent;
    font-family: monospace;
    font-size: 13px;
    color: colors.c(foreground);
  }
}

sidebar-chat-codeblock-copy {
  padding: 4px 8px;
  background: colors.c(background, 0.3);
  border: 1px solid colors.c(border);
  border-radius: 6px;
  transition: all 0.2s ease;

  &:hover {
    background: colors.c(background, 0.5);
    border-color: colors.c(primary, 0.3);
  }

  &:active {
    background: colors.c(primary, 0.1);
  }

  image {
    color: colors.c(foreground-alt);
  }
}

// Loading skeleton
sidebar-chat-message-skeletonline {
  height: 16px;
  background: linear-gradient(90deg,
      colors.c(background-elevated, 0.3) 0%,
      colors.c(background-elevated, 0.5) 50%,
      colors.c(background-elevated, 0.3) 100%);
  border-radius: 4px;
  animation: skeleton-pulse 1.5s ease-in-out infinite;

  &.sidebar-chat-message-skeletonline-offset0 {
    width: 80%;
  }

  &.sidebar-chat-message-skeletonline-offset1 {
    width: 60%;
  }

  &.sidebar-chat-message-skeletonline-offset2 {
    width: 70%;
  }
}

@keyframes skeleton-pulse {
  0% {
    opacity: 0.6;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0.6;
  }
}

// Divider
sidebar-chat-divider {
  height: 1px;
  background: colors.c(border, 0.5);
  margin: 16px 0;
}

// Input area
sidebar-chat-textarea {
  background: linear-gradient(to bottom,
      colors.c(background-elevated, 0.3),
      colors.c(background-elevated, 0.5));
  border-top: 1px solid colors.c(border, 0.3);
  padding: 12px 0;
  backdrop-filter: blur(20px);
  box-shadow: 0 -2px 10px colors.c(base, 0.1);
}

sidebar-chat-input-container {
  margin-right: 0;
  padding: 0 8px;
  // Removed fixed width to allow flexible sizing
}

sidebar-chat-input-wrapper {
  background: colors.c(background, 0.3);
  border: 1px solid colors.c(border, 0.3);
  border-radius: 26px;
  padding: 2px;
  transition: all 0.3s ease;

  &:focus-within {
    background: colors.c(background, 0.5);
    border-color: colors.c(primary, 0.5);
    box-shadow: 0 0 0 3px colors.c(primary, 0.15),
      0 2px 8px colors.c(primary, 0.2);
  }
}

sidebar-chat-entry {
  background: transparent;
  border: none;
  border-radius: 24px;
  padding: 12px 20px;
  color: colors.c(foreground);
  font-size: 14px;
  min-height: 40px;

  &:focus {
    outline: none;
    background: transparent;
    box-shadow: none;
  }

  &.typing {
    padding-right: 24px;
  }

  &::placeholder {
    color: colors.c(foreground-alt, 0.4);
    font-style: italic;
  }

  selection {
    background-color: colors.c(primary, 0.3);
    color: colors.c(foreground);
  }
}

// Send button
sidebar-chat-send {
  min-width: 30px;
  min-height: 30px;
  padding: 10px;
  background: linear-gradient(135deg, colors.c(primary, 0.4),
      colors.c(accent, 0.4));
  border-radius: 10px;
  border: 1px solid colors.c(border);
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px colors.c(primary, 0.3);

  &:hover,
  &:focus {
    background: linear-gradient(135deg, colors.c(primary, 0.6),
        colors.c(accent, 0.6));
    transform: translateY(-1px);

    border-radius: 10px;
    border: 1px solid colors.c(border-alt);
  }

  &:active {
    background: colors.c(primary);
    transform: translateY(0);
    box-shadow: 0 1px 4px colors.c(primary, 0.3);
  }

  PhosphorIcon {
    color: white;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;

    &:hover {
      transform: none;
      box-shadow: 0 2px 8px colors.c(primary, 0.3);
    }
  }
}

// System messages
sidebar-chat-system-message {
  margin: 6px 8px;
  padding: 10px;
  background: colors.c(info, 0.1);
  border: 1px solid colors.c(info, 0.2);
  border-radius: 8px;

  sidebar-chat-system-title {
    font-size: 13px;
    font-weight: 600;
    color: colors.c(info);
    margin-bottom: 4px;
  }

  sidebar-chat-system-content {
    font-size: 13px;
    color: colors.c(foreground);

    code {
      background: colors.c(base, 0.3);
      padding: 2px 4px;
      border-radius: 4px;
      font-family: monospace;
      font-size: 12px;
    }
  }
}

// Chat header
ai-chat-header {
  padding: 8px 12px;
  background: colors.c(background-elevated, 0.3);
  border-bottom: 1px solid colors.c(border, 0.3);
}

// Model selector
ai-model-selector {
  background: colors.c(background, 0.3);
  border: 1px solid colors.c(border);
  border-radius: 8px;
  padding: 8px 12px;
  transition: all 0.2s ease;

  &:hover {
    background: colors.c(background, 0.5);
    border-color: colors.c(primary, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px colors.c(base, 0.1);
  }

  &:active {
    transform: translateY(0);
  }

  label {
    font-size: 13px;
    font-weight: 500;
    color: colors.c(foreground);
  }

  PhosphorIcon {
    color: colors.c(foreground-alt);

    &:last-child {
      margin-left: 8px;
      transition: transform 0.2s ease;
    }
  }

  &:hover PhosphorIcon:last-child {
    transform: rotate(180deg);
  }
}

// Settings button (for future use)
ai-settings-btn {
  min-width: 36px;
  min-height: 36px;
  background: colors.c(background, 0.3);
  border: 1px solid colors.c(border);
  border-radius: 8px;
  transition: all 0.2s ease;

  &:hover {
    background: colors.c(background, 0.5);
    border-color: colors.c(border-alt);
  }

  image {
    color: colors.c(foreground-alt);
  }
}

// MCP server indicator (for future use)
mcp-server-status {
  padding: 4px 8px;
  background: colors.c(success, 0.1);
  border: 1px solid colors.c(success, 0.2);
  border-radius: 6px;

  &.disconnected {
    background: colors.c(error, 0.1);
    border-color: colors.c(error, 0.2);
  }

  label {
    font-size: 11px;
    font-weight: 500;
    color: colors.c(success);

    &.disconnected {
      color: colors.c(error);
    }
  }
}

// Command suggestions
command-suggestions {
  background: colors.c(background, 0.95);
  border: 1px solid colors.c(border);
  border-radius: 12px;
  margin: 0 8px 8px 8px;
  backdrop-filter: blur(20px);
  box-shadow: 0 -4px 12px colors.c(base, 0.2);
  overflow: hidden;
}

command-suggestion-item {
  background: transparent;
  border: none;
  padding: 10px 12px;
  transition: all 0.15s ease;
  border-radius: 0;

  &:hover {
    background: colors.c(background, 0.5);
  }

  &.active {
    background: colors.c(primary, 0.1);

    command-name {
      color: colors.c(primary);
    }

    command-icon {
      color: colors.c(primary);
    }
  }

  &:first-child {
    border-radius: 12px 12px 0 0;
  }

  &:last-child {
    border-radius: 0 0 12px 12px;
  }

  &:only-child {
    border-radius: 12px;
  }
}

command-icon {
  color: colors.c(foreground-alt);
  min-width: 20px;
}

command-name {
  font-family: monospace;
  font-size: 13px;
  font-weight: 600;
  color: colors.c(foreground);
}

command-desc {
  font-size: 12px;
  color: colors.c(foreground-alt);
  margin-top: 2px;
}

// Spacing helpers
.spacing-h-5 {
  >* {
    margin-right: 5px;

    &:last-child {
      margin-right: 0;
    }
  }
}

.spacing-h-10 {
  >* {
    margin-right: 10px;

    &:last-child {
      margin-right: 0;
    }
  }
}
