@use 'sass:map';
@use 'sass:color';
@use 'theme-variables';

/* <PERSON><PERSON><PERSON> Theme Colors */
// This now uses CSS variables set by theme-manager
$current-theme: dynamic;

$themes: (
  dynamic: ( // Dynamic theme using CSS variables
    // Base Colors
    base: var(--theme-base),
    surface: var(--theme-surface),
    overlay: var(--theme-overlay),

    // Text Colors
    muted: var(--theme-muted),
    subtle: var(--theme-subtle),
    text: var(--theme-text),

    // Accent Colors
    love: var(--theme-love),
    gold: var(--theme-gold),
    rose: var(--theme-rose),
    pine: var(--theme-pine),
    foam: var(--theme-foam),
    iris: var(--theme-iris),

    // Semantic Color Roles
    primary: var(--theme-primary),
    secondary: var(--theme-secondary),
    accent: var(--theme-accent),
    success: var(--theme-success),
    warning: var(--theme-warning),
    error: var(--theme-error),
    info: var(--theme-info),

    // UI Component Colors
    background: var(--theme-background),
    background-alt: var(--theme-background-alt),
    background-elevated: var(--theme-background-elevated),
    foreground: var(--theme-foreground),
    foreground-alt: var(--theme-foreground-alt),
    foreground-muted: var(--theme-foreground-muted),
    border: var(--theme-border),
    border-alt: var(--theme-border-alt),

    // State Colors
    hover: var(--theme-hover),
    active: var(--theme-active),
    focus: var(--theme-focus),
    disabled: var(--theme-disabled),
  ),
);

@function c($color, $opacity: 1) {
  $color-value: map.get(map.get($themes, $current-theme), $color);

  @if $opacity == 1 {
    @return $color-value;
  } @else {
    // Use color-mix to handle transparency with CSS variables
    $percentage: $opacity * 100%;
    @return color-mix(in srgb, #{$color-value} #{$percentage}, transparent);
  }
}

@function darken($color, $amount) {
  // Use color-mix to darken CSS variables
  $color-value: c($color);
  $percentage: 100% - $amount;
  @return color-mix(in srgb, #{$color-value} #{$percentage}, black);
}

@function lighten($color, $amount) {
  // Use color-mix to lighten CSS variables
  $color-value: c($color);
  @return color-mix(in srgb, #{$color-value} #{100% - $amount}, white #{$amount});
}


.accent {
  color: c(accent);
}
