@use "./styles/colors";
@use "./styles/theme-variables";

// TAILWIND CSS
@forward "./output.css";

// BAR CSS
@forward "./widget/bar/bar.scss";
@forward "./widget/overlays/overlays.scss";
@forward "./widget/sidebar/sidebars.scss";
@forward "./widget/bar/modules/utilities/utililies-button.scss";
@forward "./widget/launcher/launcher.scss";
@forward "./widget/utils/keyboard-shortcut.scss";
@forward "./widget/utils/context-menu.scss";
@forward "./widget/sidebar/modules/ai/ai.scss";
@forward "./widget/settings/settings.scss";
@forward "./widget/settings/monitors.scss";

// // Transparent overlay for click-outside detection
// click-outside-overlay {
//   background-color: rgba(0, 0, 0, 0.2);
//   border: none;
//
//   button {
//     background-color: transparent;
//     border: none;
//     padding: 0;
//     margin: 0;
//
//     &:hover {
//       background-color: transparent;
//     }
//   }
// }
