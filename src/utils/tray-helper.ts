// System tray helper to prevent import errors when AstalTray is not available
import { barLogger as log } from "./logger";

// Singleton for system tray
let trayInstance: any = null;
let initialized = false;
let trayAvailable = false;

export const getTray = () => {
  if (!initialized) {
    try {
      // Try to dynamically import AstalTray
      const SystemTray = eval('require("gi://AstalTray")');
      trayInstance = SystemTray.get_default();
      trayAvailable = true;
      log.info("AstalTray module loaded successfully");
    } catch (e) {
      log.info("AstalTray not available - tray module disabled");
      trayAvailable = false;
      // Return a dummy object that will no-op all operations
      trayInstance = {
        connect: () => 0, // Return a dummy signal ID
        get_items: () => [],
        get_default: () => null,
      };
    } finally {
      initialized = true;
    }
  }
  return trayInstance;
};

export const isTrayAvailable = () => {
  getTray(); // Initialize if not already done
  return trayAvailable;
};

export default getTray;
