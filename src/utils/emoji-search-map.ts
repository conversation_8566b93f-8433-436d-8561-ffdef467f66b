// Comprehensive emoji search mapping for sticker search functionality
// Organized by categories for better maintainability

// Helper function to create emoji groups
function createEmojiGroup(
  emojis: Record<string, string[]>,
): Record<string, string[]> {
  return emojis;
}

// Merge all emoji groups into one map
export const emojiSearchMap: Record<string, string[]> = {
  // ============================================
  // SMILEYS & EMOTION - HAPPY & POSITIVE
  // ============================================
  "😀": [
    "grin",
    "happy",
    "smile",
    "joy",
    "cheerful",
    "glad",
    "face",
    "teeth",
    "big smile",
  ],
  "😃": ["smiley", "happy", "smile", "big smile", "grin", "joy"],
  "😄": ["happy", "smile", "laugh", "joy", "cheerful", "grin"],
  "😁": ["beaming", "smile", "grin", "happy", "teeth", "big smile"],
  "😆": ["laugh", "lol", "haha", "funny", "hilarious", "xd"],
  "😅": ["sweat", "nervous", "awkward", "smile", "relief", "phew"],
  "🤣": ["rofl", "lmao", "laugh", "crying", "hilarious", "tears"],
  "😂": ["joy", "lol", "laugh", "cry", "tears", "funny", "lmao"],
  "🙂": ["slight smile", "smile", "happy", "content", "fine", "okay"],
  "🙃": ["upside down", "sarcasm", "silly", "playful", "ironic"],
  "😉": ["wink", "flirt", "playful", "hint", "knowing", "sly"],
  "😊": ["blush", "happy", "smile", "shy", "cute", "pleased"],
  "😇": ["angel", "innocent", "halo", "good", "blessed", "pure"],

  // Smileys & Emotion - Love & Affection
  "🥰": ["love", "hearts", "adore", "crush", "affection", "smiling"],
  "😍": ["heart eyes", "love", "crush", "adore", "infatuated", "hearts"],
  "🤩": ["star eyes", "amazed", "wow", "excited", "awesome", "stars"],
  "😘": ["kiss", "love", "xoxo", "mwah", "smooch", "blow kiss"],
  "😗": ["kiss", "smooch", "peck", "lips", "mwah"],
  "😙": ["kiss", "whistle", "smooch", "cute", "music"],
  "😚": ["kiss", "blush", "shy", "cute", "smooch", "closed eyes"],
  "☺️": ["relaxed", "happy", "content", "pleased", "smile", "blush"],
  "🥲": ["tear", "happy cry", "touched", "emotional", "proud", "joy"],

  // Smileys & Emotion - Tongue Out
  "😋": ["yum", "delicious", "tasty", "hungry", "tongue", "food"],
  "😛": ["tongue", "playful", "silly", "tease", "fun", "goofy"],
  "😜": ["wink tongue", "crazy", "wild", "playful", "silly", "fun"],
  "🤪": ["zany", "crazy", "wild", "goofy", "silly", "wacky"],
  "😝": ["tongue", "tease", "playful", "squint", "silly", "mock"],
  "🤑": ["money", "dollar", "rich", "wealthy", "cash", "greedy"],

  // Smileys & Emotion - Neutral/Thinking
  "🤗": ["hug", "embrace", "warm", "friendly", "welcome", "open arms"],
  "🤭": ["hand over mouth", "giggle", "oops", "secret", "shy", "embarrassed"],
  "🤫": ["shush", "quiet", "secret", "silence", "hush", "shh"],
  "🤔": ["think", "hmm", "wonder", "ponder", "consider", "thoughtful"],
  "🤐": ["zip", "quiet", "sealed", "secret", "silent", "closed"],
  "🤨": [
    "raised eyebrow",
    "skeptical",
    "doubt",
    "question",
    "suspicious",
    "really",
  ],
  "😐": ["neutral", "meh", "straight face", "poker face", "indifferent"],
  "😑": ["expressionless", "blank", "deadpan", "unamused", "bored"],
  "😶": ["no mouth", "silent", "speechless", "quiet", "mute"],
  "😏": ["smirk", "sly", "flirt", "confident", "knowing", "suggestive"],

  // Smileys & Emotion - Sad
  "😒": ["unamused", "side eye", "skeptical", "dissatisfied", "meh"],
  "🙄": ["eye roll", "whatever", "bored", "annoyed", "sarcastic"],
  "😬": ["grimace", "awkward", "nervous", "teeth", "cringe", "yikes"],
  "🤥": ["lying", "pinocchio", "lie", "dishonest", "long nose"],
  "😌": ["relieved", "content", "peaceful", "calm", "zen", "satisfied"],
  "😔": ["pensive", "sad", "depressed", "sorry", "regret", "disappointed"],
  "😪": ["sleepy", "tired", "snot", "sad", "tear", "exhausted"],
  "🤤": ["drool", "hungry", "desire", "want", "delicious", "craving"],
  "😴": ["sleep", "zzz", "tired", "bedtime", "snore", "rest"],

  // Smileys & Emotion - Sick/Unwell
  "😷": ["mask", "sick", "covid", "ill", "medical", "doctor"],
  "🤒": ["sick", "fever", "ill", "thermometer", "unwell", "hot"],
  "🤕": ["hurt", "injured", "bandage", "accident", "pain", "ouch"],
  "🤢": ["nauseated", "sick", "green", "ill", "disgusted", "queasy"],
  "🤮": ["vomit", "puke", "sick", "barf", "throw up", "gross"],
  "🤧": ["sneeze", "achoo", "sick", "tissue", "cold", "allergies"],
  "🥵": ["hot", "heat", "sweat", "fever", "warm", "burning"],
  "🥶": ["cold", "freeze", "frozen", "chill", "ice", "freezing"],
  "🥴": ["woozy", "drunk", "dizzy", "confused", "intoxicated"],

  // Smileys & Emotion - Concerned
  "😕": ["confused", "puzzled", "unsure", "perplexed", "uncertain"],
  "😟": ["worried", "concerned", "anxious", "nervous", "troubled"],
  "🙁": ["slightly frown", "sad", "disappointed", "unhappy", "down"],
  "☹️": ["frown", "sad", "unhappy", "disappointed", "upset"],
  "😮": ["surprised", "shocked", "wow", "oh", "amazed", "gasp"],
  "😯": ["hushed", "surprised", "wow", "amazed", "speechless"],
  "😲": ["astonished", "amazed", "shocked", "surprised", "wow"],
  "😳": ["flushed", "embarrassed", "shocked", "blush", "surprised"],
  "🥺": ["pleading", "puppy eyes", "beg", "please", "cute", "sad"],
  "😦": ["frown open", "worried", "concerned", "shocked", "dismayed"],
  "😧": ["anguished", "pained", "shocked", "horrified", "distressed"],
  "😨": ["fearful", "scared", "afraid", "worried", "anxious"],
  "😰": ["anxious", "sweat", "worried", "nervous", "stressed"],
  "😥": ["sad sweat", "disappointed", "relieved", "phew", "close call"],
  "😢": ["cry", "sad", "tear", "unhappy", "sob", "weep"],
  "😭": ["sob", "cry", "tears", "sad", "wail", "bawling"],
  "😱": ["scream", "shocked", "fear", "horror", "scared", "omg"],
  "😖": ["confounded", "frustrated", "annoyed", "scrunch", "irritated"],
  "😣": ["persevere", "struggling", "endure", "determined", "effort"],
  "😞": ["disappointed", "sad", "dejected", "unhappy", "let down"],
  "😓": ["sweat", "sad", "disappointed", "hard work", "tired"],
  "😩": ["weary", "tired", "frustrated", "exhausted", "fed up"],
  "😫": ["tired", "exhausted", "frustrated", "sleepy", "yawn"],
  "🥱": ["yawn", "tired", "sleepy", "bored", "exhausted"],

  // Smileys & Emotion - Angry
  "😤": ["triumph", "steam", "proud", "anger", "frustrated", "hmph"],
  "😡": ["angry", "mad", "rage", "furious", "red", "pissed"],
  "😠": ["angry", "mad", "annoyed", "grumpy", "irritated"],
  "🤬": ["cursing", "swear", "angry", "symbols", "@#$%", "mad"],

  // Smileys & Emotion - Devils/Monsters
  "😈": ["devil", "evil", "horns", "purple", "smile", "mischief"],
  "👿": ["angry devil", "imp", "evil", "mad", "demon", "horns"],
  "💀": ["skull", "death", "skeleton", "dead", "danger", "spooky"],
  "☠️": ["skull crossbones", "death", "danger", "poison", "pirate"],
  "💩": ["poop", "shit", "crap", "poo", "dung", "turd"],
  "🤡": ["clown", "circus", "joker", "funny", "entertainment"],
  "👹": ["ogre", "monster", "oni", "demon", "japanese", "mask"],
  "👺": ["goblin", "tengu", "angry", "mask", "japanese", "red"],
  "👻": ["ghost", "boo", "halloween", "spooky", "spirit", "phantom"],
  "👽": ["alien", "ufo", "extraterrestrial", "space", "et", "martian"],
  "👾": ["alien monster", "game", "space invader", "arcade", "pixel"],
  "🤖": ["robot", "bot", "ai", "android", "machine", "technology"],

  // Smileys & Emotion - Cats
  "😺": ["cat", "happy", "smile", "kitty", "feline", "meow"],
  "😸": ["cat grin", "happy", "smile", "kitty", "joy"],
  "😹": ["cat joy", "laugh", "tears", "lol", "kitty"],
  "😻": ["cat heart eyes", "love", "kitty", "adore", "crush"],
  "😼": ["cat smirk", "sly", "confident", "kitty", "cool"],
  "😽": ["cat kiss", "kitty", "smooch", "love", "mwah"],
  "🙀": ["cat scream", "shocked", "scared", "omg", "kitty"],
  "😿": ["cat cry", "sad", "tears", "kitty", "weep"],
  "😾": ["cat pouting", "angry", "mad", "grumpy", "kitty"],

  // Smileys & Emotion - Monkeys
  "🙈": ["see no evil", "monkey", "blind", "ignore", "shy", "embarrassed"],
  "🙉": ["hear no evil", "monkey", "deaf", "ignore", "cover ears"],
  "🙊": ["speak no evil", "monkey", "mute", "secret", "quiet", "silence"],

  // Smileys & Emotion - Hearts
  "💋": ["kiss mark", "lips", "lipstick", "kiss", "love", "smooch"],
  "💌": ["love letter", "valentine", "heart", "mail", "romance"],
  "💘": ["cupid", "heart arrow", "love", "romance", "valentine"],
  "💝": ["heart ribbon", "gift", "love", "present", "valentine"],
  "💖": ["sparkling heart", "love", "shiny", "glitter", "special"],
  "💗": ["growing heart", "love", "pink", "increase", "more"],
  "💓": ["beating heart", "pulse", "love", "heartbeat", "alive"],
  "💞": ["revolving hearts", "love", "dance", "together", "couple"],
  "💕": ["two hearts", "love", "couple", "together", "romance"],
  "💟": ["heart decoration", "love", "purple", "ornament"],
  "❣️": ["exclamation heart", "love", "emphasis", "strong"],
  "💔": ["broken heart", "sad", "breakup", "hurt", "pain"],
  "❤️": ["red heart", "love", "romance", "passion", "valentine"],
  "🧡": ["orange heart", "love", "warm", "care", "friendship"],
  "💛": ["yellow heart", "love", "friendship", "happy", "gold"],
  "💚": ["green heart", "love", "nature", "environment", "jealous"],
  "💙": ["blue heart", "love", "trust", "loyalty", "peace"],
  "💜": ["purple heart", "love", "bts", "army", "violet"],
  "🤎": ["brown heart", "love", "chocolate", "earth"],
  "🖤": ["black heart", "love", "dark", "goth", "emo"],
  "🤍": ["white heart", "love", "pure", "clean", "peace"],
  "💯": ["hundred", "100", "perfect", "score", "percent", "complete"],
  "💢": ["anger", "mad", "comic", "frustrated", "symbol"],
  "💥": ["explosion", "boom", "bang", "blast", "collision"],
  "💫": ["dizzy", "star", "sparkle", "magic", "shiny"],
  "💦": ["sweat drops", "water", "splash", "wet", "droplets"],
  "💨": ["dash", "wind", "fast", "speed", "smoke", "poof"],
  "🕳️": ["hole", "pit", "deep", "black", "void"],
  "💣": ["bomb", "explosion", "danger", "boom", "explosive"],
  "💬": ["speech bubble", "talk", "chat", "message", "conversation"],
  "👁️‍🗨️": ["eye bubble", "witness", "see", "anti bullying"],
  "🗨️": ["left speech", "talk", "chat", "bubble", "message"],
  "🗯️": ["anger bubble", "mad", "comic", "shout", "yell"],
  "💭": ["thought bubble", "think", "dream", "imagine", "ponder"],
  "💤": ["zzz", "sleep", "snore", "tired", "rest"],

  // People & Body - Hands
  "👋": ["wave", "hello", "goodbye", "hi", "bye", "hand"],
  "🤚": ["raised back hand", "stop", "high five", "halt", "hand"],
  "🖐️": ["hand", "five", "high five", "stop", "palm"],
  "✋": ["raised hand", "stop", "high five", "halt", "hand"],
  "🖖": ["vulcan", "spock", "star trek", "prosper", "hand"],
  "👌": ["ok", "okay", "perfect", "good", "fine", "yes"],
  "🤌": ["pinched fingers", "italian", "chef kiss", "gesture", "what"],
  "🤏": ["pinching", "small", "tiny", "little", "bit"],
  "✌️": ["victory", "peace", "two", "v", "win"],
  "🤞": ["crossed fingers", "luck", "hope", "wish", "lie"],
  "🤟": ["love you", "ily", "rock", "metal", "hand"],
  "🤘": ["rock on", "metal", "horns", "rock", "music"],
  "🤙": ["call", "shaka", "hang loose", "phone", "surf"],
  "👈": ["point left", "left", "direction", "that way", "there"],
  "👉": ["point right", "right", "direction", "that way", "there"],
  "👆": ["point up", "up", "above", "top", "direction"],
  "🖕": ["middle finger", "fuck", "flip off", "rude", "offensive"],
  "👇": ["point down", "down", "below", "bottom", "direction"],
  "☝️": ["index up", "one", "wait", "idea", "point"],
  "👍": ["thumbs up", "good", "like", "yes", "approve", "ok"],
  "👎": ["thumbs down", "bad", "dislike", "no", "disapprove"],
  "👊": ["fist", "punch", "bump", "brofist", "fight", "power"],
  "✊": ["raised fist", "power", "fight", "resist", "solidarity"],
  "🤛": ["left fist", "bump", "pound", "brofist", "left"],
  "🤜": ["right fist", "bump", "pound", "brofist", "right"],
  "👏": ["clap", "applause", "congrats", "bravo", "well done"],
  "🙌": ["raise hands", "hooray", "praise", "celebrate", "yay"],
  "👐": ["open hands", "hug", "jazz hands", "embrace", "open"],
  "🤲": ["palms up", "pray", "please", "receive", "give"],
  "🤝": ["handshake", "deal", "agreement", "shake", "meet"],
  "🙏": ["pray", "please", "thanks", "hope", "blessed", "namaste"],

  // People & Body - Body Parts
  "✍️": ["writing", "write", "pen", "author", "sign"],
  "💅": ["nails", "nail polish", "beauty", "manicure", "sass"],
  "🤳": ["selfie", "photo", "camera", "picture", "phone"],
  "💪": ["muscle", "strong", "flex", "arm", "workout", "power"],
  "🦾": ["mechanical arm", "prosthetic", "robot", "cyborg", "bionic"],
  "🦿": ["mechanical leg", "prosthetic", "robot", "cyborg", "bionic"],
  "🦵": ["leg", "kick", "limb", "walk", "stand"],
  "🦶": ["foot", "kick", "barefoot", "step", "walk"],
  "👂": ["ear", "hear", "listen", "sound", "hearing"],
  "🦻": ["hearing aid", "deaf", "ear", "device", "listen"],
  "👃": ["nose", "smell", "sniff", "scent", "breathe"],
  "🧠": ["brain", "think", "smart", "mind", "intelligence"],
  "🫀": ["heart organ", "anatomical", "cardio", "medical", "real heart"],
  "🫁": ["lungs", "breathe", "respiratory", "medical", "organ"],
  "🦷": ["tooth", "dentist", "teeth", "dental", "mouth"],
  "🦴": ["bone", "skeleton", "calcium", "dog", "anatomy"],
  "👀": ["eyes", "looking", "watching", "see", "look", "stare"],
  "👁️": ["eye", "see", "watch", "look", "vision", "single"],
  "👅": ["tongue", "taste", "lick", "mouth", "tease"],
  "👄": ["lips", "kiss", "mouth", "lipstick", "beauty"],

  // People & Body - People
  "👶": ["baby", "infant", "child", "newborn", "young"],
  "🧒": ["child", "kid", "young", "youth", "person"],
  "👦": ["boy", "male", "child", "son", "young"],
  "👧": ["girl", "female", "child", "daughter", "young"],
  "🧑": ["person", "adult", "human", "individual", "gender neutral"],
  "👱": ["blonde", "person", "hair", "light hair"],
  "👨": ["man", "male", "adult", "father", "guy"],
  "🧔": ["beard", "man", "facial hair", "bearded"],
  "👨‍🦰": ["red hair man", "ginger", "redhead", "man"],
  "👨‍🦱": ["curly hair man", "afro", "curls", "man"],
  "👨‍🦳": ["white hair man", "old", "gray", "elderly"],
  "👨‍🦲": ["bald man", "no hair", "hairless", "man"],
  "👩": ["woman", "female", "adult", "mother", "lady"],
  "👩‍🦰": ["red hair woman", "ginger", "redhead", "woman"],
  "👩‍🦱": ["curly hair woman", "afro", "curls", "woman"],
  "👩‍🦳": ["white hair woman", "old", "gray", "elderly"],
  "👩‍🦲": ["bald woman", "no hair", "hairless", "woman"],
  "👴": ["old man", "elderly", "grandpa", "senior", "grandfather"],
  "👵": ["old woman", "elderly", "grandma", "senior", "grandmother"],

  // People & Body - Gestures & Activities
  "🙍": ["frown", "sad", "disappointed", "person frowning"],
  "🙍‍♂️": ["man frown", "sad", "disappointed", "unhappy"],
  "🙍‍♀️": ["woman frown", "sad", "disappointed", "unhappy"],
  "🙎": ["pouting", "upset", "annoyed", "person pouting"],
  "🙎‍♂️": ["man pouting", "upset", "annoyed", "grumpy"],
  "🙎‍♀️": ["woman pouting", "upset", "annoyed", "grumpy"],
  "🙅": ["no good", "stop", "reject", "x", "person gesturing no"],
  "🙅‍♂️": ["man no", "stop", "reject", "refuse"],
  "🙅‍♀️": ["woman no", "stop", "reject", "refuse"],
  "🙆": ["ok gesture", "yes", "agree", "person gesturing ok"],
  "🙆‍♂️": ["man ok", "yes", "agree", "approve"],
  "🙆‍♀️": ["woman ok", "yes", "agree", "approve"],
  "💁": ["information", "help", "sassy", "person tipping hand"],
  "💁‍♂️": ["man tipping hand", "sassy", "information", "help"],
  "💁‍♀️": ["woman tipping hand", "sassy", "information", "help"],
  "🙋": ["raise hand", "question", "answer", "person raising hand"],
  "🙋‍♂️": ["man raising hand", "question", "answer", "volunteer"],
  "🙋‍♀️": ["woman raising hand", "question", "answer", "volunteer"],
  "🧏": ["deaf", "hear", "sign language", "person deaf"],
  "🧏‍♂️": ["deaf man", "hearing", "sign language"],
  "🧏‍♀️": ["deaf woman", "hearing", "sign language"],
  "🙇": ["bow", "sorry", "respect", "person bowing"],
  "🙇‍♂️": ["man bow", "sorry", "respect", "apology"],
  "🙇‍♀️": ["woman bow", "sorry", "respect", "apology"],
  "🤦": ["facepalm", "disbelief", "embarrassed", "person facepalming"],
  "🤦‍♂️": ["man facepalm", "disbelief", "embarrassed", "stupid"],
  "🤦‍♀️": ["woman facepalm", "disbelief", "embarrassed", "stupid"],
  "🤷": ["shrug", "dunno", "whatever", "person shrugging"],
  "🤷‍♂️": ["man shrug", "dunno", "idk", "whatever"],
  "🤷‍♀️": ["woman shrug", "dunno", "idk", "whatever"],

  // Activities & Sports
  "🏃": ["run", "running", "exercise", "jog", "person running"],
  "🏃‍♂️": ["man run", "running", "exercise", "jog"],
  "🏃‍♀️": ["woman run", "running", "exercise", "jog"],
  "🚶": ["walk", "walking", "stroll", "person walking"],
  "🚶‍♂️": ["man walk", "walking", "stroll", "pedestrian"],
  "🚶‍♀️": ["woman walk", "walking", "stroll", "pedestrian"],
  "🧍": ["stand", "standing", "person standing", "still"],
  "🧍‍♂️": ["man stand", "standing", "still", "upright"],
  "🧍‍♀️": ["woman stand", "standing", "still", "upright"],
  "🧎": ["kneel", "kneeling", "pray", "person kneeling"],
  "🧎‍♂️": ["man kneel", "kneeling", "pray", "propose"],
  "🧎‍♀️": ["woman kneel", "kneeling", "pray", "propose"],

  // Animals
  "🐶": ["dog face", "puppy", "pet", "cute", "woof", "bark"],
  "🐱": ["cat face", "kitty", "pet", "meow", "feline", "cute"],
  "🐭": ["mouse face", "mice", "rodent", "cute", "small"],
  "🐹": ["hamster", "pet", "cute", "rodent", "small"],
  "🐰": ["rabbit face", "bunny", "cute", "easter", "hop"],
  "🦊": ["fox", "sly", "clever", "cunning", "orange"],
  "🐻": ["bear", "teddy", "grizzly", "brown", "cute"],
  "🐼": ["panda", "cute", "china", "bamboo", "bear"],
  "🐨": ["koala", "australia", "cute", "eucalyptus", "bear"],
  "🐯": ["tiger face", "cat", "wild", "stripes", "fierce"],
  "🦁": ["lion", "king", "mane", "wild", "roar"],
  "🐮": ["cow face", "moo", "milk", "farm", "cattle"],
  "🐷": ["pig face", "oink", "pork", "farm", "pink"],
  "🐸": ["frog", "toad", "ribbit", "hop", "green"],
  "🐵": ["monkey face", "ape", "primate", "banana", "curious"],
  "🐔": ["chicken", "hen", "rooster", "bird", "farm"],
  "🐧": ["penguin", "antarctica", "cold", "waddle", "tux"],
  "🐦": ["bird", "tweet", "fly", "chirp", "wings"],
  "🐤": ["baby chick", "chicken", "cute", "yellow", "small"],
  "🦆": ["duck", "quack", "pond", "water", "bird"],
  "🦅": ["eagle", "america", "freedom", "bird", "soar"],
  "🦉": ["owl", "hoot", "night", "wise", "bird"],
  "🦇": ["bat", "vampire", "night", "halloween", "fly"],
  "🐺": ["wolf", "howl", "wild", "pack", "moon"],
  "🐗": ["boar", "wild pig", "tusks", "forest"],
  "🐴": ["horse face", "pony", "ride", "gallop", "neigh"],
  "🦄": ["unicorn", "magic", "fantasy", "rainbow", "mythical"],
  "🐝": ["bee", "honey", "buzz", "insect", "busy"],
  "🐛": ["bug", "caterpillar", "insect", "worm", "crawl"],
  "🦋": ["butterfly", "beautiful", "transform", "flutter", "insect"],
  "🐌": ["snail", "slow", "shell", "spiral", "slug"],
  "🐞": ["ladybug", "beetle", "insect", "lucky", "red"],
  "🦟": ["mosquito", "bug", "bite", "annoying", "insect"],
  "🦗": ["cricket", "chirp", "insect", "jump", "bug"],
  "🕷️": ["spider", "web", "arachnid", "eight legs", "creepy"],
  "🕸️": ["spider web", "cobweb", "halloween", "trap"],
  "🦂": ["scorpion", "sting", "desert", "dangerous", "arachnid"],
  "🐢": ["turtle", "slow", "shell", "tortoise", "reptile"],
  "🐍": ["snake", "serpent", "slither", "reptile", "hiss"],
  "🦎": ["lizard", "reptile", "gecko", "chameleon"],
  "🦖": ["t-rex", "dinosaur", "extinct", "roar", "prehistoric"],
  "🦕": ["dinosaur", "sauropod", "long neck", "extinct", "prehistoric"],
  "🐙": ["octopus", "tentacles", "ocean", "eight", "sea"],
  "🦑": ["squid", "calamari", "ocean", "tentacles", "sea"],
  "🦐": ["shrimp", "prawn", "seafood", "ocean", "small"],
  "🦞": ["lobster", "seafood", "ocean", "claws", "red"],
  "🦀": ["crab", "seafood", "ocean", "claws", "sideways"],
  "🐡": ["blowfish", "pufferfish", "ocean", "spiky", "fish"],
  "🐠": ["tropical fish", "ocean", "colorful", "aquarium", "swim"],
  "🐟": ["fish", "seafood", "ocean", "swim", "aquatic"],
  "🐬": ["dolphin", "ocean", "smart", "flipper", "marine"],
  "🐳": ["whale", "ocean", "spout", "big", "marine"],
  "🐋": ["whale", "ocean", "blue whale", "big", "marine"],
  "🦈": ["shark", "ocean", "jaws", "dangerous", "predator"],
  "🐊": ["crocodile", "alligator", "reptile", "dangerous", "swamp"],
  "🐅": ["tiger", "wild", "stripes", "cat", "fierce"],
  "🐆": ["leopard", "spots", "wild", "cat", "fast"],
  "🦓": ["zebra", "stripes", "africa", "horse", "wild"],
  "🦍": ["gorilla", "ape", "strong", "primate", "harambe"],
  "🦧": ["orangutan", "ape", "primate", "orange", "tree"],
  "🐘": ["elephant", "trunk", "big", "memory", "africa"],
  "🦛": ["hippo", "hippopotamus", "river", "africa", "big"],
  "🦏": ["rhino", "rhinoceros", "horn", "africa", "endangered"],
  "🐪": ["camel", "desert", "hump", "dromedary", "arabia"],
  "🐫": ["two hump camel", "bactrian", "desert", "asia"],
  "🦒": ["giraffe", "tall", "neck", "africa", "spots"],
  "🦘": ["kangaroo", "australia", "jump", "pouch", "joey"],
  "🦬": ["bison", "buffalo", "american", "plains", "wild"],
  "🐃": ["water buffalo", "ox", "asia", "horns", "farm"],
  "🐂": ["ox", "bull", "cattle", "strong", "farm"],
  "🐄": ["cow", "moo", "milk", "farm", "cattle"],
  "🐎": ["horse", "gallop", "ride", "race", "fast"],
  "🐖": ["pig", "oink", "farm", "pork", "mud"],
  "🐏": ["ram", "sheep", "male", "horns", "farm"],
  "🐑": ["sheep", "ewe", "wool", "farm", "baa"],
  "🦙": ["llama", "alpaca", "spit", "andes", "wool"],
  "🐐": ["goat", "farm", "horns", "climb", "kid"],
  "🦌": ["deer", "bambi", "antlers", "forest", "wild"],
  "🐕": ["dog", "pet", "woof", "loyal", "friend"],
  "🐩": ["poodle", "dog", "fancy", "groomed", "french"],
  "🦮": ["guide dog", "service", "blind", "helper", "trained"],
  "🐕‍🦺": ["service dog", "vest", "working", "helper", "trained"],
  "🐈": ["cat", "pet", "meow", "feline", "kitty"],
  "🐈‍⬛": ["black cat", "halloween", "superstition", "feline"],
  "🦃": ["turkey", "thanksgiving", "gobble", "bird", "holiday"],
  "🦚": ["peacock", "beautiful", "feathers", "pride", "bird"],
  "🦜": ["parrot", "tropical", "talk", "colorful", "bird"],
  "🦢": ["swan", "graceful", "lake", "white", "bird"],
  "🦤": ["dodo", "extinct", "bird", "gone", "flightless"],
  "🦩": ["flamingo", "pink", "tropical", "bird", "legs"],
  "🕊️": ["dove", "peace", "bird", "white", "fly"],
  "🐇": ["rabbit", "bunny", "hop", "easter", "fast"],
  "🦝": ["raccoon", "trash panda", "mask", "night", "clever"],
  "🦨": ["skunk", "stink", "smell", "spray", "black white"],
  "🦡": ["badger", "honey badger", "fierce", "dig", "tough"],
  "🦫": ["beaver", "dam", "wood", "build", "teeth"],
  "🦦": ["otter", "cute", "water", "playful", "swim"],
  "🦥": ["sloth", "slow", "lazy", "tree", "hang"],
  "🐁": ["mouse", "rodent", "small", "cheese", "quiet"],
  "🐀": ["rat", "rodent", "pest", "lab", "city"],
  "🦔": ["hedgehog", "spiky", "cute", "sonic", "roll"],

  // Food & Drink
  "🍏": ["green apple", "fruit", "healthy", "granny smith"],
  "🍎": ["red apple", "fruit", "healthy", "teacher"],
  "🍐": ["pear", "fruit", "green", "sweet"],
  "🍊": ["orange", "tangerine", "fruit", "citrus", "vitamin c"],
  "🍋": ["lemon", "sour", "citrus", "yellow", "zest"],
  "🍌": ["banana", "fruit", "yellow", "potassium", "monkey"],
  "🍉": ["watermelon", "fruit", "summer", "juicy", "red"],
  "🍇": ["grapes", "fruit", "wine", "purple", "bunch"],
  "🍓": ["strawberry", "berry", "red", "sweet", "fruit"],
  "🫐": ["blueberries", "berry", "fruit", "antioxidant", "blue"],
  "🍈": ["melon", "cantaloupe", "fruit", "sweet"],
  "🍒": ["cherries", "fruit", "red", "sweet", "pair"],
  "🍑": ["peach", "fruit", "fuzzy", "sweet", "butt"],
  "🥭": ["mango", "tropical", "fruit", "sweet", "yellow"],
  "🍍": ["pineapple", "tropical", "fruit", "hawaii", "sweet"],
  "🥥": ["coconut", "tropical", "white", "milk", "palm"],
  "🥝": ["kiwi", "fruit", "green", "fuzzy", "new zealand"],
  "🍅": ["tomato", "vegetable", "red", "salad", "sauce"],
  "🍆": ["eggplant", "aubergine", "purple", "vegetable"],
  "🥑": ["avocado", "guacamole", "green", "healthy", "toast"],
  "🥦": ["broccoli", "vegetable", "green", "healthy", "tree"],
  "🥬": ["leafy green", "lettuce", "salad", "healthy", "vegetable"],
  "🥒": ["cucumber", "pickle", "green", "vegetable", "salad"],
  "🌶️": ["hot pepper", "chili", "spicy", "red", "hot"],
  "🫑": ["bell pepper", "capsicum", "vegetable", "sweet"],
  "🌽": ["corn", "maize", "yellow", "vegetable", "cob"],
  "🥕": ["carrot", "orange", "vegetable", "bunny", "healthy"],
  "🫒": ["olive", "mediterranean", "oil", "green", "black"],
  "🧄": ["garlic", "vampire", "flavor", "cooking", "smell"],
  "🧅": ["onion", "cry", "layers", "vegetable", "cooking"],
  "🥔": ["potato", "spud", "vegetable", "starch", "fries"],
  "🍠": ["sweet potato", "yam", "orange", "vegetable"],
  "🥐": ["croissant", "french", "pastry", "breakfast", "bakery"],
  "🥖": ["baguette", "french", "bread", "long", "bakery"],
  "🍞": ["bread", "loaf", "bakery", "sandwich", "toast"],
  "🥨": ["pretzel", "salty", "twisted", "snack", "german"],
  "🥯": ["bagel", "breakfast", "hole", "bread", "jewish"],
  "🥞": ["pancakes", "breakfast", "syrup", "stack", "flapjacks"],
  "🧇": ["waffle", "breakfast", "syrup", "belgian", "grid"],
  "🧀": ["cheese", "wedge", "dairy", "yellow", "mouse"],
  "🍖": ["meat", "bone", "steak", "bbq", "protein"],
  "🍗": ["poultry leg", "chicken", "turkey", "drumstick", "meat"],
  "🥩": ["steak", "meat", "beef", "rare", "protein"],
  "🥓": ["bacon", "breakfast", "pork", "crispy", "strips"],
  "🍔": ["hamburger", "burger", "fast food", "beef", "sandwich"],
  "🍟": ["fries", "french fries", "potato", "fast food", "chips"],
  "🍕": ["pizza", "slice", "italian", "cheese", "pepperoni"],
  "🌭": ["hot dog", "sausage", "american", "baseball", "mustard"],
  "🥪": ["sandwich", "lunch", "bread", "sub", "hoagie"],
  "🌮": ["taco", "mexican", "shell", "tuesday", "beef"],
  "🌯": ["burrito", "wrap", "mexican", "beans", "rolled"],
  "��": ["tamale", "mexican", "corn", "wrapped", "traditional"],
  "🥙": ["stuffed flatbread", "kebab", "gyro", "pita", "mediterranean"],
  "🧆": ["falafel", "chickpea", "middle eastern", "vegetarian"],
  "🥚": ["egg", "breakfast", "protein", "shell", "oval"],
  "🍳": ["cooking", "fried egg", "breakfast", "pan", "sunny side"],
  "🥘": ["shallow pan", "paella", "food", "cooking", "dish"],
  "🍲": ["pot", "stew", "soup", "hot", "cooking"],
  "🫕": ["fondue", "cheese", "chocolate", "melted", "swiss"],
  "🥣": ["bowl", "cereal", "soup", "breakfast", "spoon"],
  "🥗": ["salad", "green", "healthy", "vegetables", "bowl"],
  "🍿": ["popcorn", "movie", "snack", "cinema", "kernel"],
  "🧈": ["butter", "dairy", "spread", "yellow", "melted"],
  "🧂": ["salt", "seasoning", "shaker", "sodium", "white"],
  "🥫": ["canned food", "preserved", "tin", "soup", "beans"],
  "🍱": ["bento", "japanese", "lunch box", "meal", "compartments"],
  "🍘": ["rice cracker", "senbei", "japanese", "snack"],
  "🍙": ["rice ball", "onigiri", "japanese", "triangle"],
  "🍚": ["rice", "bowl", "cooked", "white", "asian"],
  "🍛": ["curry", "rice", "indian", "japanese", "spicy"],
  "🍜": ["ramen", "noodles", "soup", "japanese", "bowl"],
  "🍝": ["spaghetti", "pasta", "italian", "noodles", "tomato"],
  "🍢": ["oden", "skewer", "japanese", "kebab", "stick"],
  "🍣": ["sushi", "japanese", "fish", "rice", "nigiri"],
  "🍤": ["fried shrimp", "tempura", "japanese", "prawn"],
  "🍥": ["fish cake", "narutomaki", "japanese", "swirl", "pink"],
  "🥮": ["moon cake", "chinese", "festival", "pastry", "autumn"],
  "🍡": ["dango", "japanese", "sweet", "skewer", "mochi"],
  "🥟": ["dumpling", "chinese", "potsticker", "gyoza", "dim sum"],
  "🥠": ["fortune cookie", "chinese", "message", "lucky", "crispy"],
  "🥡": ["takeout box", "chinese", "food", "delivery", "container"],
  "🦪": ["oyster", "shellfish", "pearl", "seafood", "raw"],
  "🍦": ["ice cream", "soft serve", "dessert", "cone", "swirl"],
  "🍧": ["shaved ice", "snow cone", "dessert", "syrup", "cold"],
  "🍨": ["ice cream", "bowl", "dessert", "sundae", "scoop"],
  "🍩": ["donut", "doughnut", "dessert", "sweet", "hole"],
  "🍪": ["cookie", "biscuit", "dessert", "chocolate chip", "sweet"],
  "🎂": ["birthday cake", "cake", "celebration", "candles", "party"],
  "🍰": ["cake", "slice", "dessert", "sweet", "shortcake"],
  "🧁": ["cupcake", "muffin", "dessert", "sweet", "frosting"],
  "🥧": ["pie", "dessert", "slice", "pastry", "filling"],
  "🍫": ["chocolate", "bar", "candy", "sweet", "brown"],
  "🍬": ["candy", "sweet", "wrapper", "sugar", "treat"],
  "🍭": ["lollipop", "candy", "sweet", "stick", "sucker"],
  "🍮": ["custard", "flan", "dessert", "pudding", "caramel"],
  "🍯": ["honey", "pot", "bee", "sweet", "golden"],
  "🍼": ["baby bottle", "milk", "infant", "feeding", "baby"],
  "🥛": ["milk", "glass", "dairy", "white", "calcium"],
  "☕": ["coffee", "hot", "mug", "caffeine", "morning"],
  "🫖": ["teapot", "tea", "hot", "brew", "ceramic"],
  "🍵": ["tea", "green tea", "matcha", "hot", "cup"],
  "🍶": ["sake", "japanese", "alcohol", "rice wine", "bottle"],
  "🍾": ["champagne", "bottle", "celebration", "pop", "bubbly"],
  "🍷": ["wine", "glass", "red wine", "alcohol", "drink"],
  "🍸": ["cocktail", "martini", "glass", "drink", "olive"],
  "🍹": ["tropical drink", "cocktail", "vacation", "umbrella", "fruity"],
  "🍺": ["beer", "mug", "alcohol", "drink", "cheers"],
  "🍻": ["beers", "cheers", "toast", "celebration", "mugs"],
  "🥂": ["clinking glasses", "cheers", "toast", "champagne", "celebration"],
  "🥃": ["tumbler glass", "whiskey", "scotch", "rocks", "neat"],
  "🥤": ["cup with straw", "soda", "drink", "soft drink", "beverage"],
  "🧋": ["bubble tea", "boba", "tapioca", "taiwanese", "milk tea"],
  "🧃": ["juice box", "drink", "straw", "kids", "fruit"],
  "🧉": ["mate", "tea", "yerba", "south american", "straw"],
  "🧊": ["ice", "cube", "cold", "frozen", "chill"],

  // Travel & Places
  "🌍": [
    "earth africa",
    "globe",
    "world",
    "planet",
    "international",
    "global",
    "africa",
    "europe",
  ],
  "🌎": [
    "earth americas",
    "globe",
    "world",
    "planet",
    "western",
    "america",
    "usa",
    "canada",
  ],
  "🌏": [
    "earth asia",
    "globe",
    "world",
    "planet",
    "eastern",
    "asia",
    "australia",
    "pacific",
  ],
  "🌐": [
    "globe meridians",
    "world",
    "international",
    "www",
    "internet",
    "web",
    "global",
    "network",
  ],
  "🗺️": [
    "world map",
    "geography",
    "travel",
    "navigation",
    "atlas",
    "cartography",
    "location",
  ],
  "🗾": [
    "japan map",
    "country",
    "island",
    "asia",
    "silhouette",
    "nippon",
    "japanese",
  ],
  "🧭": [
    "compass",
    "direction",
    "navigation",
    "north",
    "travel",
    "navigate",
    "orientation",
  ],
  "🏔️": [
    "mountain",
    "snow",
    "peak",
    "hiking",
    "tall",
    "alps",
    "summit",
    "snowy",
  ],
  "⛰️": [
    "mountain",
    "hiking",
    "nature",
    "peak",
    "climb",
    "hill",
    "trek",
    "mountaineering",
  ],
  "🌋": [
    "volcano",
    "eruption",
    "lava",
    "hot",
    "mountain",
    "magma",
    "erupt",
    "volcanic",
  ],
  "🗻": [
    "mount fuji",
    "japan",
    "mountain",
    "snow",
    "sacred",
    "fuji",
    "japanese mountain",
  ],
  "🏕️": [
    "camping",
    "tent",
    "outdoors",
    "nature",
    "adventure",
    "camp",
    "wilderness",
    "campsite",
  ],
  "🏖️": [
    "beach",
    "umbrella",
    "vacation",
    "sand",
    "ocean",
    "seaside",
    "coast",
    "holiday",
  ],
  "🏜️": ["desert", "cactus", "hot", "dry", "sand", "sahara", "arid", "dune"],
  "🏝️": [
    "island",
    "tropical",
    "palm tree",
    "vacation",
    "paradise",
    "deserted island",
    "caribbean",
  ],
  "🏞️": [
    "national park",
    "nature",
    "landscape",
    "scenery",
    "outdoors",
    "park",
    "scenic",
    "vista",
  ],

  // Buildings & Architecture
  "🏟️": [
    "stadium",
    "sports",
    "arena",
    "venue",
    "colosseum",
    "football",
    "concert",
  ],
  "🏛️": [
    "classical building",
    "museum",
    "greek",
    "columns",
    "architecture",
    "government",
    "temple",
  ],
  "🏗️": [
    "construction",
    "building",
    "crane",
    "work",
    "site",
    "development",
    "build",
  ],
  "🧱": [
    "brick",
    "wall",
    "construction",
    "build",
    "masonry",
    "block",
    "material",
  ],
  "🏘️": [
    "houses",
    "neighborhood",
    "suburb",
    "residential",
    "community",
    "village",
    "homes",
  ],
  "🏚️": [
    "derelict house",
    "abandoned",
    "old",
    "ruined",
    "broken",
    "haunted",
    "decrepit",
  ],
  "🏠": [
    "house",
    "home",
    "building",
    "residence",
    "dwelling",
    "household",
    "family",
  ],
  "🏡": [
    "house garden",
    "home",
    "yard",
    "cottage",
    "suburban",
    "property",
    "residence",
  ],
  "🏢": [
    "office",
    "building",
    "business",
    "work",
    "corporate",
    "company",
    "skyscraper",
  ],
  "🏣": [
    "japanese post office",
    "mail",
    "postal",
    "japan",
    "correspondence",
    "letter",
  ],
  "🏤": [
    "post office",
    "mail",
    "postal",
    "letters",
    "package",
    "correspondence",
    "europe",
  ],
  "🏥": [
    "hospital",
    "medical",
    "health",
    "emergency",
    "clinic",
    "healthcare",
    "doctor",
  ],
  "🏦": [
    "bank",
    "money",
    "finance",
    "building",
    "financial",
    "savings",
    "institution",
  ],
  "🏨": [
    "hotel",
    "accommodation",
    "lodging",
    "stay",
    "vacation",
    "hospitality",
    "inn",
  ],
  "🏩": [
    "love hotel",
    "romance",
    "heart",
    "accommodation",
    "japanese",
    "couples",
  ],
  "🏪": [
    "convenience store",
    "shop",
    "24/7",
    "market",
    "retail",
    "minimart",
    "open",
  ],
  "🏫": [
    "school",
    "education",
    "building",
    "learn",
    "study",
    "academic",
    "classroom",
  ],
  "🏬": [
    "department store",
    "shopping",
    "mall",
    "retail",
    "commerce",
    "shop",
    "store",
  ],
  "🏭": [
    "factory",
    "industrial",
    "manufacturing",
    "production",
    "industry",
    "plant",
    "smoke",
  ],
  "🏯": [
    "japanese castle",
    "pagoda",
    "fortress",
    "historic",
    "architecture",
    "samurai",
  ],
  "🏰": [
    "castle",
    "fortress",
    "medieval",
    "palace",
    "kingdom",
    "royal",
    "fairy tale",
  ],
  "💒": [
    "wedding",
    "church",
    "chapel",
    "marriage",
    "ceremony",
    "love",
    "romance",
  ],
  "🗼": [
    "tokyo tower",
    "landmark",
    "japan",
    "tourist",
    "observation",
    "red tower",
  ],
  "🗽": [
    "statue of liberty",
    "new york",
    "usa",
    "freedom",
    "america",
    "landmark",
    "liberty",
  ],
  "⛪": [
    "church",
    "christian",
    "religion",
    "worship",
    "chapel",
    "cathedral",
    "cross",
  ],
  "🕌": [
    "mosque",
    "islam",
    "muslim",
    "worship",
    "religion",
    "minaret",
    "prayer",
  ],
  "🛕": [
    "hindu temple",
    "mandir",
    "worship",
    "religion",
    "india",
    "hinduism",
    "spiritual",
  ],
  "🕍": [
    "synagogue",
    "jewish",
    "temple",
    "worship",
    "judaism",
    "star of david",
  ],
  "⛩️": [
    "shinto shrine",
    "torii",
    "japan",
    "gate",
    "religious",
    "spiritual",
    "red gate",
  ],
  "🕋": ["kaaba", "mecca", "islam", "muslim", "hajj", "pilgrimage", "holy"],

  // Transportation
  "🚂": [
    "locomotive",
    "train",
    "steam",
    "railway",
    "engine",
    "railroad",
    "choo choo",
  ],
  "🚃": [
    "railway car",
    "train",
    "carriage",
    "rail",
    "transport",
    "passenger",
    "coach",
  ],
  "🚄": [
    "high speed train",
    "bullet train",
    "shinkansen",
    "fast",
    "railway",
    "japan",
  ],
  "🚅": [
    "bullet train",
    "shinkansen",
    "fast",
    "railway",
    "speed",
    "express",
    "rapid",
  ],
  "🚆": ["train", "railway", "metro", "subway", "transport", "commute", "rail"],
  "🚇": [
    "metro",
    "subway",
    "underground",
    "tube",
    "train",
    "urban",
    "transport",
  ],
  "🚈": [
    "light rail",
    "tram",
    "streetcar",
    "public transport",
    "city",
    "commute",
  ],
  "🚉": [
    "station",
    "train station",
    "railway",
    "platform",
    "depot",
    "terminal",
    "stop",
  ],
  "🚊": ["tram", "streetcar", "trolley", "public transport", "city", "rail"],
  "🚝": [
    "monorail",
    "train",
    "elevated",
    "transport",
    "railway",
    "single rail",
  ],
  "🚞": [
    "mountain railway",
    "cable car",
    "funicular",
    "train",
    "steep",
    "incline",
  ],
  "🚋": ["tram car", "streetcar", "trolley", "carriage", "public transport"],
  "🚌": [
    "bus",
    "public transport",
    "coach",
    "transit",
    "school bus",
    "vehicle",
  ],
  "🚍": [
    "bus front",
    "oncoming bus",
    "transport",
    "vehicle",
    "approaching",
    "public",
  ],
  "🚎": [
    "trolleybus",
    "electric bus",
    "public transport",
    "city",
    "eco",
    "green",
  ],
  "🚐": ["minibus", "van", "shuttle", "transport", "vehicle", "small bus"],
  "🚑": [
    "ambulance",
    "emergency",
    "medical",
    "hospital",
    "rescue",
    "emt",
    "911",
  ],
  "🚒": [
    "fire engine",
    "fire truck",
    "emergency",
    "rescue",
    "firefighter",
    "ladder",
  ],
  "🚓": [
    "police car",
    "cop",
    "patrol",
    "law enforcement",
    "emergency",
    "cruiser",
  ],
  "🚔": ["police car front", "oncoming", "cop", "law", "patrol", "approaching"],
  "🚕": ["taxi", "cab", "ride", "transport", "yellow cab", "hire", "fare"],
  "🚖": [
    "taxi front",
    "oncoming taxi",
    "cab",
    "approaching",
    "ride",
    "transport",
  ],
  "🚗": ["car", "automobile", "vehicle", "drive", "sedan", "red car", "auto"],
  "🚘": [
    "car front",
    "oncoming",
    "automobile",
    "approaching",
    "vehicle",
    "drive",
  ],
  "🚙": ["suv", "recreational vehicle", "rv", "camping", "road trip", "4x4"],
  "🚚": [
    "delivery truck",
    "lorry",
    "shipping",
    "cargo",
    "freight",
    "transport",
  ],
  "🚛": [
    "articulated lorry",
    "semi",
    "truck",
    "18 wheeler",
    "big rig",
    "hauler",
  ],
  "🚜": [
    "tractor",
    "farm",
    "agriculture",
    "farming",
    "vehicle",
    "rural",
    "harvest",
  ],
  "🏎️": [
    "racing car",
    "f1",
    "formula one",
    "speed",
    "race",
    "fast",
    "motorsport",
  ],
  "🏍️": ["motorcycle", "motorbike", "bike", "ride", "speed", "biker", "harley"],
  "🛵": ["motor scooter", "vespa", "moped", "transport", "urban", "delivery"],
  "🛺": [
    "auto rickshaw",
    "tuk tuk",
    "three wheeler",
    "taxi",
    "india",
    "transport",
  ],
  "🚲": ["bicycle", "bike", "cycling", "ride", "pedal", "exercise", "eco"],
  "🛴": ["kick scooter", "scooter", "ride", "transport", "push", "kids"],
  "🛹": ["skateboard", "skate", "board", "trick", "sport", "ride", "deck"],
  "🛼": ["roller skate", "skating", "rollerblade", "wheels", "disco", "retro"],
  "🚏": ["bus stop", "transit", "wait", "public transport", "station", "sign"],
  "🛣️": ["motorway", "highway", "freeway", "road", "interstate", "route"],
  "🛤️": [
    "railway track",
    "railroad",
    "train tracks",
    "rails",
    "transportation",
  ],
  "🛢️": ["oil drum", "barrel", "petroleum", "fuel", "energy", "container"],
  "⛽": ["fuel pump", "gas station", "petrol", "gasoline", "refuel", "fill up"],
  "🚨": [
    "police car light",
    "siren",
    "emergency",
    "alert",
    "warning",
    "beacon",
  ],
  "🚥": ["traffic light", "horizontal", "signal", "stop", "go", "intersection"],
  "🚦": ["traffic light", "vertical", "signal", "stop", "go", "red light"],
  "🛑": ["stop sign", "octagonal", "halt", "stop", "road sign", "traffic"],
  "🚧": ["construction", "barrier", "roadwork", "warning", "caution", "detour"],

  // Air & Sea Transport
  "⚓": ["anchor", "ship", "boat", "maritime", "navy", "nautical", "harbor"],
  "⛵": ["sailboat", "sailing", "yacht", "boat", "wind", "nautical", "vessel"],
  "🛶": ["canoe", "kayak", "paddle", "boat", "river", "rowing", "water"],
  "🚤": ["speedboat", "motorboat", "fast", "boat", "racing", "water", "marine"],
  "🛳️": ["passenger ship", "cruise", "liner", "vessel", "ocean", "travel"],
  "⛴️": ["ferry", "boat", "ship", "transport", "crossing", "passenger"],
  "🛥️": ["motor boat", "yacht", "speedboat", "luxury", "vessel", "marine"],
  "🚢": ["ship", "boat", "vessel", "ocean", "cruise", "maritime", "sailing"],
  "✈️": ["airplane", "plane", "flight", "travel", "fly", "aircraft", "jet"],
  "🛩️": ["small airplane", "propeller", "cessna", "private plane", "aircraft"],
  "🛫": [
    "airplane departure",
    "takeoff",
    "flight",
    "leaving",
    "airport",
    "ascend",
  ],
  "🛬": [
    "airplane arrival",
    "landing",
    "flight",
    "arriving",
    "airport",
    "descend",
  ],
  "🪂": ["parachute", "skydiving", "jump", "fall", "extreme sport", "deploy"],
  "💺": ["seat", "chair", "airplane seat", "sit", "passenger", "travel"],
  "🚁": ["helicopter", "chopper", "rotor", "fly", "aircraft", "rescue"],
  "🚟": ["suspension railway", "cable car", "gondola", "aerial", "mountain"],
  "🚠": ["mountain cableway", "cable car", "gondola", "ski lift", "aerial"],
  "🚡": ["aerial tramway", "cable car", "gondola", "mountain", "ski"],
  "🛰️": ["satellite", "space", "orbit", "communication", "gps", "technology"],
  "🚀": ["rocket", "space", "launch", "spaceship", "nasa", "blast off", "moon"],
  "🛸": ["ufo", "flying saucer", "alien", "spaceship", "extraterrestrial"],

  // Time & Clock
  "🕐": ["one oclock", "1:00", "time", "clock", "hour", "schedule"],
  "🕑": ["two oclock", "2:00", "time", "clock", "hour", "schedule"],
  "🕒": ["three oclock", "3:00", "time", "clock", "hour", "schedule"],
  "🕓": ["four oclock", "4:00", "time", "clock", "hour", "schedule"],
  "🕔": ["five oclock", "5:00", "time", "clock", "hour", "schedule"],
  "🕕": ["six oclock", "6:00", "time", "clock", "hour", "schedule"],
  "🕖": ["seven oclock", "7:00", "time", "clock", "hour", "schedule"],
  "🕗": ["eight oclock", "8:00", "time", "clock", "hour", "schedule"],
  "🕘": ["nine oclock", "9:00", "time", "clock", "hour", "schedule"],
  "🕙": ["ten oclock", "10:00", "time", "clock", "hour", "schedule"],
  "🕚": ["eleven oclock", "11:00", "time", "clock", "hour", "schedule"],
  "🕛": ["twelve oclock", "12:00", "noon", "midnight", "time", "clock"],
  "🕧": ["twelve thirty", "12:30", "half past", "time", "clock", "schedule"],
  "🕰️": ["mantelpiece clock", "antique", "time", "vintage", "old", "classic"],
  "⏰": ["alarm clock", "wake up", "morning", "time", "ring", "alert"],
  "⏱️": ["stopwatch", "timer", "sports", "time", "race", "measure"],
  "⏲️": ["timer clock", "kitchen timer", "countdown", "cooking", "time"],
  "⌛": ["hourglass", "time", "sand", "wait", "timer", "flowing"],
  "⏳": ["hourglass flowing", "time", "wait", "loading", "progress", "sand"],
  "⌚": ["watch", "time", "wrist", "accessory", "timepiece", "clock"],

  // Weather
  "☀️": ["sun", "sunny", "weather", "hot", "bright", "day", "sunshine"],
  "🌤️": ["sun behind small cloud", "partly cloudy", "weather", "mostly sunny"],
  "⛅": ["sun behind cloud", "partly cloudy", "weather", "cloudy", "overcast"],
  "🌥️": ["sun behind large cloud", "mostly cloudy", "weather", "overcast"],
  "☁️": ["cloud", "cloudy", "weather", "overcast", "sky", "gray"],
  "🌦️": [
    "sun behind rain cloud",
    "sun shower",
    "weather",
    "mixed",
    "partly rainy",
  ],
  "🌧️": [
    "cloud with rain",
    "rainy",
    "weather",
    "precipitation",
    "wet",
    "shower",
  ],
  "⛈️": [
    "cloud with lightning and rain",
    "thunderstorm",
    "storm",
    "weather",
    "thunder",
  ],
  "🌩️": ["cloud with lightning", "thunder", "storm", "weather", "electric"],
  "🌨️": ["cloud with snow", "snowing", "weather", "winter", "cold", "snowfall"],
  "❄️": ["snowflake", "snow", "winter", "cold", "frozen", "ice", "christmas"],
  "☃️": ["snowman", "winter", "snow", "cold", "frosty", "christmas"],
  "⛄": ["snowman without snow", "winter", "cold", "frosty", "holiday"],
  "🌬️": ["wind face", "blow", "breeze", "weather", "windy", "gust"],
  "🌪️": ["tornado", "twister", "cyclone", "weather", "storm", "disaster"],
  "🌫️": ["fog", "mist", "weather", "haze", "visibility", "foggy"],
  "🌊": ["wave", "ocean", "water", "sea", "tsunami", "surf", "splash"],
  "💧": ["droplet", "water", "drop", "tear", "rain", "liquid", "wet"],
  "☂️": ["umbrella", "rain", "weather", "protection", "parasol"],
  "🌈": ["rainbow", "colors", "pride", "weather", "spectrum", "arc", "lgbtq"],
  "🌡️": ["thermometer", "temperature", "hot", "cold", "weather", "fever"],

  // Nature & Environment
  "🌱": ["seedling", "plant", "sprout", "grow", "spring", "new", "growth"],
  "🌲": ["evergreen tree", "pine", "forest", "christmas tree", "conifer"],
  "🌳": ["deciduous tree", "tree", "forest", "nature", "oak", "shade"],
  "🌴": ["palm tree", "tropical", "beach", "coconut", "vacation", "island"],
  "🌵": ["cactus", "desert", "plant", "succulent", "dry", "prickly"],
  "🌾": ["sheaf of rice", "grain", "wheat", "harvest", "agriculture", "crop"],
  "🌿": ["herb", "plant", "leaf", "green", "medicine", "cooking"],
  "☘️": ["shamrock", "clover", "irish", "luck", "three leaf", "ireland"],
  "🍀": ["four leaf clover", "lucky", "fortune", "irish", "rare", "luck"],
  "🍁": ["maple leaf", "canada", "autumn", "fall", "tree", "canadian"],
  "🍂": ["fallen leaves", "autumn", "fall", "season", "october", "pile"],
  "🍃": ["leaf fluttering", "wind", "nature", "green", "breeze", "spring"],
  "🌺": ["hibiscus", "flower", "tropical", "hawaii", "bloom", "red"],
  "🌻": ["sunflower", "flower", "yellow", "summer", "bright", "sun"],
  "🌹": ["rose", "flower", "love", "romance", "red", "valentine"],
  "🥀": ["wilted flower", "dead", "dying", "sad", "breakup", "withered"],
  "🌷": ["tulip", "flower", "spring", "holland", "bloom", "bulb"],
  "🌼": ["daisy", "flower", "white", "spring", "bloom", "simple"],
  "🌸": ["cherry blossom", "sakura", "flower", "spring", "japan", "pink"],
  "💐": ["bouquet", "flowers", "gift", "romance", "arrangement", "celebration"],
  "🌙": ["crescent moon", "night", "sleep", "dark", "lunar", "sky"],
  "🌛": ["first quarter moon face", "night", "sleep", "creepy", "lunar"],
  "🌜": ["last quarter moon face", "night", "sleep", "creepy", "lunar"],
  "🌚": ["new moon face", "dark", "night", "creepy", "solar eclipse"],
  "🌝": ["full moon face", "bright", "night", "creepy", "lunar"],
  "🌞": ["sun with face", "bright", "sunny", "happy", "summer", "warm"],
  "⭐": ["star", "night", "sky", "bright", "sparkle", "favorite"],
  "🌟": ["glowing star", "sparkle", "shiny", "bright", "special", "magic"],
  "✨": ["sparkles", "magic", "shiny", "stars", "glitter", "special", "clean"],
  "⚡": [
    "lightning",
    "electricity",
    "thunder",
    "bolt",
    "power",
    "shock",
    "fast",
  ],
  "🔥": ["fire", "hot", "flame", "burn", "lit", "heat", "spicy"],
  "☄️": ["comet", "space", "meteor", "shooting star", "asteroid"],
  "🌑": ["new moon", "dark", "night", "lunar", "phase", "eclipse"],
  "🌒": ["waxing crescent moon", "lunar", "phase", "night", "growing"],
  "🌓": ["first quarter moon", "lunar", "phase", "half", "night"],
  "🌔": ["waxing gibbous moon", "lunar", "phase", "almost full", "night"],
  "🌕": ["full moon", "lunar", "bright", "night", "werewolf", "complete"],
  "🌖": ["waning gibbous moon", "lunar", "phase", "decreasing", "night"],
  "🌗": ["last quarter moon", "lunar", "phase", "half", "night"],
  "🌘": ["waning crescent moon", "lunar", "phase", "ending", "night"],

  // Objects & Tools
  "🔨": ["hammer", "tool", "build", "construction", "fix", "repair", "nail"],
  "🪓": ["axe", "hatchet", "chop", "wood", "tool", "lumberjack", "cut"],
  "⛏️": ["pick", "pickaxe", "mining", "dig", "tool", "minecraft", "excavate"],
  "🔧": ["wrench", "tool", "fix", "repair", "spanner", "mechanic", "adjust"],
  "🪛": [
    "screwdriver",
    "tool",
    "screw",
    "fix",
    "repair",
    "phillips",
    "flathead",
  ],
  "🔩": ["nut and bolt", "hardware", "tool", "fasten", "screw", "construction"],
  "⚙️": ["gear", "settings", "cog", "mechanical", "machine", "engineering"],
  "🪜": ["ladder", "climb", "step", "reach", "height", "tool", "ascend"],
  "🔫": ["pistol", "gun", "water gun", "squirt", "toy", "weapon", "shoot"],
  "🏹": ["bow and arrow", "archery", "weapon", "hunt", "sport", "archer"],
  "🗡️": ["dagger", "knife", "blade", "weapon", "sharp", "sword", "stab"],
  "⚔️": ["crossed swords", "battle", "fight", "war", "duel", "combat"],
  "🛡️": ["shield", "protection", "defense", "guard", "armor", "security"],
  "🔑": ["key", "unlock", "access", "password", "security", "open", "lock"],
  "🗝️": ["old key", "antique", "vintage", "unlock", "mystery", "classic"],
  "🔓": ["unlocked", "open", "unlock", "access", "unsecured", "available"],
  "🔒": ["locked", "secure", "private", "closed", "security", "protected"],
  "🔐": ["locked with key", "secure", "encrypted", "private", "protected"],
  "🔏": ["locked with pen", "secure", "private", "confidential", "signed"],
  "📎": ["paperclip", "attach", "clip", "office", "fasten", "document"],
  "🔗": ["link", "chain", "url", "connection", "hyperlink", "connect"],
  "⛓️": ["chains", "link", "connect", "bound", "metal", "locked"],
  "🧰": ["toolbox", "tools", "repair", "fix", "maintenance", "kit"],
  "🧲": ["magnet", "attraction", "magnetic", "pull", "physics", "metal"],
  "⚗️": ["alembic", "chemistry", "science", "lab", "experiment", "distill"],
  "🧪": ["test tube", "science", "chemistry", "experiment", "lab", "research"],
  "🧫": ["petri dish", "biology", "science", "bacteria", "culture", "lab"],
  "🧬": ["dna", "genetics", "biology", "science", "helix", "life"],
  "🔬": ["microscope", "science", "research", "lab", "biology", "study"],
  "🔭": ["telescope", "astronomy", "space", "stars", "observation", "science"],
  "📡": ["satellite antenna", "communication", "signal", "broadcast", "dish"],

  // Electronics & Technology
  "💻": ["laptop", "computer", "pc", "work", "technology", "device", "macbook"],
  "🖥️": ["desktop computer", "pc", "monitor", "work", "technology", "imac"],
  "🖨️": ["printer", "print", "document", "paper", "office", "device"],
  "⌨️": ["keyboard", "type", "typing", "computer", "input", "keys"],
  "🖱️": ["computer mouse", "click", "cursor", "device", "input", "pointer"],
  "🖲️": ["trackball", "mouse", "input", "device", "control", "cursor"],
  "💾": ["floppy disk", "save", "storage", "retro", "old", "data"],
  "💿": ["optical disk", "cd", "dvd", "disc", "storage", "music"],
  "📀": ["dvd", "disk", "movie", "video", "storage", "media"],
  "🧮": ["abacus", "calculation", "math", "count", "ancient", "calculator"],
  "📱": ["mobile phone", "smartphone", "iphone", "cell", "device", "call"],
  "📲": ["mobile phone arrow", "receive", "call", "incoming", "message"],
  "☎️": ["telephone", "phone", "call", "landline", "contact", "ring"],
  "📞": ["telephone receiver", "phone", "call", "handset", "contact"],
  "📟": ["pager", "beeper", "message", "alert", "retro", "communication"],
  "📠": ["fax machine", "fax", "document", "send", "office", "communication"],
  "🔋": ["battery", "power", "energy", "charge", "electric", "full"],
  "🔌": [
    "electric plug",
    "power",
    "socket",
    "electricity",
    "connect",
    "charge",
  ],
  "💡": ["light bulb", "idea", "bright", "electricity", "innovation", "eureka"],
  "🔦": ["flashlight", "torch", "light", "dark", "beam", "illuminate"],
  "🕯️": ["candle", "light", "flame", "wax", "romantic", "illuminate"],
  "🪔": ["diya lamp", "oil lamp", "diwali", "light", "festival", "hindu"],
  "📷": ["camera", "photo", "picture", "photography", "snapshot", "capture"],
  "📸": ["camera flash", "photo", "picture", "selfie", "capture", "snapshot"],
  "📹": ["video camera", "record", "film", "movie", "camcorder", "video"],
  "📼": ["videocassette", "vhs", "tape", "retro", "movie", "record"],
  "🔍": ["magnifying glass left", "search", "zoom", "find", "investigate"],
  "🔎": ["magnifying glass right", "search", "zoom", "find", "investigate"],
  "🕮": ["book", "read", "literature", "study", "knowledge", "library"],
  "📚": ["books", "library", "study", "education", "reading", "stack"],
  "📖": ["open book", "reading", "study", "education", "knowledge", "pages"],
  "📰": ["newspaper", "news", "press", "media", "article", "journalism"],
  "📜": ["scroll", "ancient", "document", "paper", "parchment", "decree"],
  "📄": ["page facing up", "document", "paper", "file", "text", "sheet"],
  "📃": ["page with curl", "document", "paper", "file", "contract"],
  "📑": ["bookmark tabs", "organize", "index", "reference", "tabs"],
  "🔖": ["bookmark", "save", "mark", "tag", "label", "reference"],
  "📊": ["bar chart", "graph", "statistics", "data", "analytics", "report"],
  "📈": ["chart increasing", "growth", "profit", "stocks", "up", "trend"],
  "📉": ["chart decreasing", "loss", "decline", "stocks", "down", "trend"],
  "📋": ["clipboard", "checklist", "task", "document", "board", "notes"],
  "📌": ["pushpin", "pin", "tack", "attach", "note", "bulletin"],
  "📍": ["round pushpin", "location", "pin", "map", "place", "marker"],
  "📏": ["straight ruler", "measure", "length", "tool", "school", "line"],
  "📐": ["triangular ruler", "geometry", "math", "measure", "angle", "school"],
  "✂️": ["scissors", "cut", "snip", "trim", "craft", "tool"],
  "🖊️": ["pen", "write", "ballpoint", "ink", "sign", "writing"],
  "🖋️": ["fountain pen", "write", "calligraphy", "ink", "fancy", "sign"],
  "✒️": ["black nib", "pen", "write", "calligraphy", "ink", "quill"],
  "🖌️": ["paintbrush", "paint", "art", "brush", "artist", "create"],
  "🖍️": ["crayon", "draw", "color", "art", "child", "wax"],
  "✏️": ["pencil", "write", "draw", "sketch", "school", "lead"],

  // Symbols & Signs
  "❤️‍🩹": ["mending heart", "healing", "recovery", "getting better", "bandage"],
  "♉": ["taurus", "zodiac", "bull", "astrology", "april", "may", "earth sign"],
  "♊": ["gemini", "zodiac", "twins", "astrology", "may", "june", "air sign"],
  "♋": ["cancer", "zodiac", "crab", "astrology", "june", "july", "water sign"],
  "♌": ["leo", "zodiac", "lion", "astrology", "july", "august", "fire sign"],
  "♍": [
    "virgo",
    "zodiac",
    "maiden",
    "astrology",
    "august",
    "september",
    "earth sign",
  ],
  "♎": [
    "libra",
    "zodiac",
    "scales",
    "astrology",
    "september",
    "october",
    "air sign",
  ],
  "♏": [
    "scorpio",
    "zodiac",
    "scorpion",
    "astrology",
    "october",
    "november",
    "water sign",
  ],
  "♐": [
    "sagittarius",
    "zodiac",
    "archer",
    "astrology",
    "november",
    "december",
    "fire sign",
  ],
  "♑": [
    "capricorn",
    "zodiac",
    "goat",
    "astrology",
    "december",
    "january",
    "earth sign",
  ],
  "♒": [
    "aquarius",
    "zodiac",
    "water bearer",
    "astrology",
    "january",
    "february",
    "air sign",
  ],
  "♓": [
    "pisces",
    "zodiac",
    "fish",
    "astrology",
    "february",
    "march",
    "water sign",
  ],
  "⛎": [
    "ophiuchus",
    "zodiac",
    "serpent",
    "astrology",
    "13th sign",
    "snake bearer",
  ],
  "🔴": ["red circle", "dot", "round", "stop", "record", "alert"],
  "🟠": ["orange circle", "dot", "round", "orange", "circle"],
  "🟡": ["yellow circle", "dot", "round", "yellow", "gold", "circle"],
  "🟢": ["green circle", "dot", "round", "go", "online", "available"],
  "🔵": ["blue circle", "dot", "round", "blue", "circle"],
  "🟣": ["purple circle", "dot", "round", "purple", "violet", "circle"],
  "⚫": ["black circle", "dot", "round", "dark", "filled", "bullet"],
  "⚪": ["white circle", "dot", "round", "empty", "blank", "outline"],
  "🟤": ["brown circle", "dot", "round", "brown", "circle"],
  "▪️": ["black small square", "box", "filled", "small", "bullet"],
  "▫️": ["white small square", "box", "empty", "small", "checkbox"],
  "◾": ["black medium square", "box", "filled", "medium", "square"],
  "◽": ["white medium square", "box", "empty", "medium", "square"],
  "◼️": ["black medium square", "box", "filled", "square", "block"],
  "◻️": ["white medium square", "box", "empty", "square", "blank"],
  "⬛": ["black large square", "box", "filled", "large", "square", "block"],
  "⬜": ["white large square", "box", "empty", "large", "square", "blank"],
  "🔶": ["large orange diamond", "shape", "orange", "diamond", "warning"],
  "🔷": ["large blue diamond", "shape", "blue", "diamond", "gem"],
  "🔸": ["small orange diamond", "shape", "orange", "diamond", "bullet"],
  "🔹": ["small blue diamond", "shape", "blue", "diamond", "bullet"],
  "🔺": ["red triangle up", "up", "arrow", "increase", "point"],
  "🔻": ["red triangle down", "down", "arrow", "decrease", "point"],
  "✅": ["check mark", "done", "complete", "yes", "correct", "tick", "approve"],
  "❌": ["cross mark", "x", "no", "wrong", "delete", "cancel", "incorrect"],
  "❓": ["question mark", "question", "confused", "ask", "what", "why", "?"],
  "❗": ["exclamation mark", "important", "alert", "attention", "warning", "!"],
  "‼️": ["double exclamation", "very important", "urgent", "alert", "!!"],
  "⁉️": ["exclamation question", "surprised", "what", "shocked", "?!"],
  "🚫": ["prohibited", "no", "forbidden", "banned", "not allowed", "stop"],
  "🚷": ["no pedestrians", "forbidden", "no walking", "prohibited"],
  "🚯": ["no littering", "forbidden", "trash", "prohibited", "don't"],
  "🚳": ["no bicycles", "forbidden", "bikes", "prohibited", "cycling"],
  "🚱": ["non-potable water", "no drinking", "unsafe", "water"],
  "📵": ["no mobile phones", "forbidden", "prohibited", "silence"],
  "🔞": ["18+", "nsfw", "adult", "mature", "restricted", "age limit"],
  "🆗": ["ok button", "okay", "good", "yes", "agree", "fine"],
  "🆙": ["up button", "level up", "upgrade", "increase", "higher"],
  "🆒": ["cool button", "awesome", "nice", "good", "great"],
  "🆕": ["new button", "fresh", "latest", "recent", "updated"],
  "🆓": ["free button", "no cost", "gratis", "complimentary"],
  "🅰️": ["a button", "blood type", "letter a", "grade"],
  "🅱️": ["b button", "blood type", "letter b", "meme"],
  "🅾️": ["o button", "blood type", "letter o", "zero"],
  "🆎": ["ab button", "blood type", "type ab"],
  "🅿️": ["p button", "parking", "park", "car"],

  // Flags (Sample)
  "🏳️": ["white flag", "surrender", "give up", "peace", "truce"],
  "🏴": ["black flag", "pirate", "anarchist", "protest"],
  "🏁": ["checkered flag", "race", "finish", "complete", "racing", "f1"],
  "🚩": ["triangular flag", "red flag", "warning", "alert", "golf"],
  "🏳️‍🌈": ["rainbow flag", "pride", "lgbtq", "gay", "diversity", "equality"],
  "🏳️‍⚧️": ["transgender flag", "trans", "pride", "lgbtq", "gender"],
  "🏴‍☠️": ["pirate flag", "jolly roger", "skull", "crossbones", "pirate"],
  "🇺🇸": [
    "usa flag",
    "america",
    "united states",
    "us",
    "american",
    "stars and stripes",
  ],
  "🇬🇧": [
    "uk flag",
    "britain",
    "england",
    "united kingdom",
    "british",
    "union jack",
  ],
  "🇨🇦": ["canada flag", "canadian", "maple leaf", "ca", "north america"],
  "🇦🇺": ["australia flag", "australian", "aussie", "down under", "oceania"],
  "🇯🇵": ["japan flag", "japanese", "nippon", "rising sun", "jp"],
  "🇰🇷": ["south korea flag", "korean", "korea", "kr", "seoul"],
  "🇨🇳": ["china flag", "chinese", "cn", "beijing", "prc"],
  "🇮🇳": ["india flag", "indian", "bharat", "in", "tricolor"],
  "🇩🇪": ["germany flag", "german", "deutschland", "de", "berlin"],
  "🇫🇷": ["france flag", "french", "fr", "paris", "tricolore"],
  "🇮🇹": ["italy flag", "italian", "it", "rome", "italia"],
  "🇪🇸": ["spain flag", "spanish", "es", "españa", "madrid"],
  "🇧🇷": ["brazil flag", "brazilian", "br", "brasil", "portuguese"],
  "🇲🇽": ["mexico flag", "mexican", "mx", "mexico city", "español"],
  "🇷🇺": ["russia flag", "russian", "ru", "moscow", "kremlin"],

  // Activities & Events
  "🎃": [
    "jack-o-lantern",
    "halloween",
    "pumpkin",
    "spooky",
    "october",
    "carved",
  ],
  "🎄": ["christmas tree", "xmas", "holiday", "december", "festive", "pine"],
  "🎆": ["fireworks", "celebration", "new year", "4th of july", "explosion"],
  "🎇": ["sparkler", "firework", "celebration", "handheld", "sparks"],
  "🧨": ["firecracker", "dynamite", "explosive", "boom", "chinese new year"],
  "🎈": ["balloon", "party", "birthday", "celebration", "red", "float"],
  "🎉": ["party popper", "celebration", "tada", "confetti", "congratulations"],
  "🎊": ["confetti ball", "celebration", "party", "festive", "congratulations"],
  "🎋": ["tanabata tree", "wish", "festival", "japanese", "bamboo"],
  "🎍": ["pine decoration", "kadomatsu", "new year", "japanese", "bamboo"],
  "🎎": ["dolls", "hinamatsuri", "japanese", "festival", "girls day"],
  "🎏": [
    "carp streamer",
    "koinobori",
    "children's day",
    "japanese",
    "fish flag",
  ],
  "🎐": ["wind chime", "furin", "summer", "japanese", "bell", "breeze"],
  "🎑": ["moon viewing", "tsukimi", "harvest moon", "japanese", "autumn"],
  "🧧": ["red envelope", "lucky money", "chinese new year", "hongbao", "gift"],
  "🎀": ["ribbon", "bow", "gift", "pink", "decoration", "cute"],
  "🎁": ["gift", "present", "birthday", "wrapped", "surprise", "box"],
  "🎗️": ["reminder ribbon", "awareness", "support", "cause", "yellow ribbon"],
  "🎟️": ["admission ticket", "ticket", "event", "concert", "entry", "pass"],
  "🎫": ["ticket", "admission", "event", "stub", "entry", "pass"],
  "🎖️": ["military medal", "honor", "award", "achievement", "valor"],
  "🏆": ["trophy", "winner", "champion", "first place", "award", "gold"],
  "🏅": ["sports medal", "gold", "silver", "bronze", "award", "olympics"],
  "🥇": ["1st place medal", "gold", "winner", "first", "champion"],
  "🥈": ["2nd place medal", "silver", "second", "runner up"],
  "🥉": ["3rd place medal", "bronze", "third", "podium"],
  "⚽": ["soccer ball", "football", "sport", "kick", "goal", "world cup"],
  "⚾": ["baseball", "sport", "ball", "pitch", "mlb", "home run"],
  "🥎": ["softball", "sport", "ball", "pitch", "game"],
  "🏀": ["basketball", "sport", "hoop", "nba", "dribble", "court"],
  "🏐": ["volleyball", "sport", "beach", "net", "serve", "spike"],
  "🏈": ["american football", "nfl", "sport", "touchdown", "superbowl"],
  "🏉": ["rugby", "sport", "ball", "scrum", "tackle"],
  "🎾": ["tennis", "sport", "ball", "racket", "court", "wimbledon"],
  "🥏": ["flying disc", "frisbee", "sport", "throw", "ultimate"],
  "🎳": ["bowling", "strike", "sport", "pins", "alley", "spare"],
  "🏏": ["cricket", "sport", "bat", "wicket", "game"],
  "🏑": ["field hockey", "sport", "stick", "game", "puck"],
  "🏒": ["ice hockey", "sport", "stick", "puck", "nhl", "rink"],
  "🥍": ["lacrosse", "sport", "stick", "net", "game"],
  "🏓": ["ping pong", "table tennis", "paddle", "sport", "ball"],
  "🏸": ["badminton", "racquet", "shuttlecock", "sport", "net"],
  "🥊": ["boxing glove", "fight", "punch", "sport", "boxer"],
  "🥋": ["martial arts uniform", "karate", "judo", "taekwondo", "gi"],
  "🥅": ["goal net", "soccer", "hockey", "score", "sport"],
  "⛳": ["golf", "flag", "hole", "sport", "putting green", "course"],
  "⛸️": ["ice skate", "skating", "figure skating", "winter", "rink"],
  "🎣": ["fishing", "pole", "rod", "fish", "angling", "sport"],
  "🤿": ["diving mask", "snorkel", "swimming", "underwater", "scuba"],
  "🎽": ["running shirt", "singlet", "athletics", "marathon", "sport"],
  "🎿": ["skis", "skiing", "winter sport", "snow", "alpine", "downhill"],
  "🛷": ["sled", "sledge", "toboggan", "winter", "snow", "slide"],
  "🥌": ["curling stone", "curling", "ice", "sport", "winter", "sweep"],
  "🎯": ["direct hit", "bullseye", "target", "dart", "aim", "perfect"],
  "🪀": ["yo-yo", "toy", "play", "string", "trick", "up and down"],
  "🪁": ["kite", "fly", "wind", "string", "toy", "sky"],
  "🎱": ["8 ball", "pool", "billiards", "magic 8 ball", "game"],
  "🔮": ["crystal ball", "fortune", "future", "magic", "psychic", "mystic"],
  "🪄": ["magic wand", "wizard", "spell", "magic", "trick", "abracadabra"],
  "🎮": ["video game", "controller", "gaming", "console", "play", "xbox"],
  "🕹️": ["joystick", "arcade", "gaming", "retro", "control", "atari"],
  "🎰": ["slot machine", "casino", "gambling", "jackpot", "777", "vegas"],
  "🎲": ["dice", "die", "gambling", "random", "board game", "roll"],
  "🧩": ["jigsaw", "puzzle piece", "autism", "connect", "solve", "game"],
  "🧸": ["teddy bear", "toy", "stuffed animal", "cute", "childhood", "plush"],
  "🪅": ["piñata", "party", "celebration", "mexican", "candy", "hit"],
  "🪆": ["nesting dolls", "matryoshka", "russian", "toy", "babushka"],
  "♠️": ["spades", "cards", "suit", "poker", "black", "ace"],
  "♥️": ["hearts", "cards", "suit", "poker", "red", "love"],
  "♦️": ["diamonds", "cards", "suit", "poker", "red", "gem"],
  "♣️": ["clubs", "cards", "suit", "poker", "black", "clover"],
  "♟️": ["chess pawn", "game", "strategy", "board", "piece"],
  "🃏": ["joker", "card", "wild", "fool", "jester", "playing card"],
  "🀄": ["mahjong", "red dragon", "tile", "chinese", "game"],
  "🎴": ["flower playing cards", "hanafuda", "japanese", "game", "cards"],

  // Music & Arts
  "🎵": ["musical note", "music", "sound", "melody", "sing", "quarter note"],
  "🎶": ["musical notes", "music", "melody", "song", "tune", "notes"],
  "🎼": ["musical score", "sheet music", "composition", "notes", "staff"],
  "🎤": ["microphone", "sing", "karaoke", "voice", "mic", "speech"],
  "🎧": ["headphones", "music", "listen", "audio", "earphones", "sound"],
  "📻": ["radio", "music", "broadcast", "fm", "am", "tuner"],
  "🎷": ["saxophone", "jazz", "music", "brass", "instrument", "sax"],
  "🪗": ["accordion", "music", "instrument", "polka", "squeeze box"],
  "🎸": ["guitar", "music", "rock", "instrument", "electric", "acoustic"],
  "🎹": ["piano", "keyboard", "music", "instrument", "keys", "classical"],
  "🎺": ["trumpet", "music", "brass", "instrument", "jazz", "horn"],
  "🎻": ["violin", "music", "classical", "instrument", "strings", "fiddle"],
  "🪕": ["banjo", "music", "country", "instrument", "bluegrass", "strings"],
  "🥁": ["drum", "music", "percussion", "beat", "rhythm", "drums"],
  "🪘": ["long drum", "conga", "percussion", "music", "african", "bongo"],
  "🎨": ["artist palette", "art", "paint", "colors", "creative", "painting"],
  "🖼️": ["framed picture", "art", "painting", "museum", "gallery", "portrait"],
  "🎭": ["performing arts", "theater", "drama", "masks", "comedy tragedy"],
  "🎬": ["clapper board", "movie", "film", "action", "cinema", "take"],
  "🎞️": ["film frames", "movie", "cinema", "reel", "35mm", "motion picture"],

  // Clothing & Accessories
  "👓": ["glasses", "eyeglasses", "spectacles", "vision", "see", "nerd"],
  "🕶️": ["sunglasses", "cool", "shades", "summer", "sunny", "style"],
  "🥽": ["goggles", "swimming", "safety", "lab", "protection", "ski"],
  "🥼": [
    "lab coat",
    "doctor",
    "scientist",
    "medical",
    "white coat",
    "professional",
  ],
  "🦺": ["safety vest", "construction", "visibility", "high vis", "worker"],
  "👔": ["necktie", "business", "formal", "professional", "suit", "tie"],
  "👕": ["t-shirt", "tshirt", "casual", "clothing", "top", "tee"],
  "👖": ["jeans", "pants", "denim", "trousers", "bottoms", "legs"],
  "🧣": ["scarf", "winter", "warm", "neck", "cold", "accessory"],
  "🧤": ["gloves", "hands", "winter", "warm", "mittens", "cold"],
  "🧥": ["coat", "jacket", "winter", "outerwear", "warm", "clothing"],
  "🧦": ["socks", "feet", "clothing", "pair", "warm", "stockings"],
  "👗": ["dress", "clothing", "fashion", "woman", "outfit", "gown"],
  "👘": ["kimono", "japanese", "traditional", "clothing", "robe"],
  "🥻": ["sari", "indian", "traditional", "clothing", "woman", "saree"],
  "🩱": ["one-piece swimsuit", "swimming", "beach", "pool", "bathing suit"],
  "🩲": ["briefs", "underwear", "swimming", "speedos", "trunks"],
  "🩳": ["shorts", "summer", "clothing", "bottoms", "casual", "legs"],
  "👙": ["bikini", "swimsuit", "beach", "swimming", "two piece", "summer"],
  "👚": ["woman's clothes", "blouse", "shirt", "top", "clothing"],
  "👛": ["purse", "wallet", "clutch", "money", "bag", "accessory"],
  "👜": ["handbag", "purse", "bag", "fashion", "accessory", "tote"],
  "👝": ["clutch bag", "purse", "small", "evening", "party", "accessory"],
  "🛍️": ["shopping bags", "retail", "purchase", "mall", "buy", "shop"],
  "🎒": ["backpack", "school", "bag", "rucksack", "hiking", "student"],
  "🩴": ["thong sandal", "flip flop", "beach", "summer", "footwear", "casual"],
  "👞": ["man's shoe", "dress shoe", "formal", "leather", "business", "oxford"],
  "👟": ["running shoe", "sneaker", "athletic", "trainer", "sport", "gym"],
  "🥾": ["hiking boot", "boot", "outdoor", "walking", "mountain", "trek"],
  "🥿": ["flat shoe", "ballet flat", "woman", "casual", "comfort", "slip on"],
  "👠": ["high heel", "stiletto", "shoe", "fashion", "woman", "pump"],
  "👡": ["woman's sandal", "heel", "summer", "shoe", "open toe", "strap"],
  "🩰": ["ballet shoes", "dance", "pointe", "ballerina", "pink", "slippers"],
  "👢": ["woman's boot", "boot", "fashion", "winter", "knee high", "footwear"],
  "👑": ["crown", "king", "queen", "royal", "ruler", "monarchy"],
  "👒": ["woman's hat", "sun hat", "fashion", "summer", "bow", "bonnet"],
  "🎩": ["top hat", "magic", "gentleman", "formal", "magician", "fancy"],
  "🎓": [
    "graduation cap",
    "mortarboard",
    "graduate",
    "college",
    "degree",
    "education",
  ],
  "🧢": ["billed cap", "baseball cap", "hat", "casual", "sport", "snapback"],
  "🪖": ["military helmet", "army", "soldier", "combat", "war", "protection"],
  "⛑️": [
    "rescue helmet",
    "safety",
    "emergency",
    "red cross",
    "help",
    "medical",
  ],
  "💄": ["lipstick", "makeup", "cosmetics", "beauty", "red", "lips"],
  "💍": ["ring", "engagement", "wedding", "diamond", "proposal", "jewelry"],
  "💎": ["gem stone", "diamond", "jewel", "precious", "crystal", "luxury"],

  // Miscellaneous Objects
  "🔇": ["muted", "silent", "no sound", "quiet", "volume off", "mute"],
  "🔈": ["speaker low", "volume", "quiet", "sound", "audio", "soft"],
  "🔉": ["speaker medium", "volume", "sound", "audio", "moderate"],
  "🔊": ["speaker high", "loud", "volume", "sound", "audio", "max"],
  "📢": ["loudspeaker", "announcement", "megaphone", "public", "broadcast"],
  "📣": ["megaphone", "cheering", "announcement", "loud", "shout", "rally"],
  "📯": ["postal horn", "trumpet", "announcement", "fanfare", "bugle"],
  "🔔": ["bell", "ring", "notification", "alarm", "ding", "chime"],
  "🔕": ["bell slash", "silent", "no notifications", "mute", "quiet"],
  "💹": [
    "chart increasing yen",
    "graph",
    "growth",
    "finance",
    "profit",
    "trending up",
  ],
  "🏧": ["atm", "bank", "cash", "money", "withdraw", "automated teller"],
  "🚮": ["litter bin", "trash", "garbage", "waste", "dispose", "rubbish"],
  "🚰": ["potable water", "faucet", "tap", "drinking", "sink", "water"],
  "♿": ["wheelchair", "accessible", "disability", "handicap", "mobility"],
  "🚹": ["men's room", "bathroom", "restroom", "toilet", "male", "lavatory"],
  "🚺": [
    "women's room",
    "bathroom",
    "restroom",
    "toilet",
    "female",
    "lavatory",
  ],
  "🚻": ["restroom", "bathroom", "toilet", "wc", "lavatory", "facilities"],
  "🚼": ["baby symbol", "changing", "nursery", "infant", "diaper", "family"],
  "🚾": ["water closet", "wc", "toilet", "bathroom", "restroom", "loo"],
  "🛂": ["passport control", "immigration", "customs", "border", "travel"],
  "🛃": ["customs", "luggage", "inspection", "border", "declaration"],
  "🛄": ["baggage claim", "luggage", "airport", "carousel", "arrival"],
  "🛅": ["left luggage", "locker", "storage", "baggage", "deposit"],
  "⚠️": ["warning", "caution", "alert", "danger", "hazard", "attention"],
  "🚸": ["children crossing", "caution", "school", "kids", "slow", "safety"],
  "⛔": ["no entry", "prohibited", "forbidden", "stop", "do not enter"],
  "☢️": ["radioactive", "nuclear", "danger", "hazard", "radiation", "toxic"],
  "☣️": ["biohazard", "danger", "toxic", "biological", "hazard", "infectious"],
  "⬆️": ["up arrow", "north", "increase", "upward", "direction", "top"],
  "↗️": ["up-right arrow", "northeast", "diagonal", "direction"],
  "➡️": ["right arrow", "east", "next", "forward", "direction", "point"],
  "↘️": ["down-right arrow", "southeast", "diagonal", "direction"],
  "⬇️": ["down arrow", "south", "decrease", "downward", "direction", "bottom"],
  "↙️": ["down-left arrow", "southwest", "diagonal", "direction"],
  "⬅️": ["left arrow", "west", "back", "previous", "direction", "return"],
  "↖️": ["up-left arrow", "northwest", "diagonal", "direction"],
  "↕️": ["up-down arrow", "vertical", "bidirectional", "sort", "resize"],
  "↔️": ["left-right arrow", "horizontal", "bidirectional", "resize", "swap"],
  "↩️": ["right arrow curving left", "return", "undo", "reply", "back"],
  "↪️": ["left arrow curving right", "forward", "redo", "share", "send"],
  "⤴️": ["right arrow curving up", "level up", "upload", "upward", "rise"],
  "⤵️": ["right arrow curving down", "download", "downward", "descend"],
  "🔃": ["clockwise arrows", "refresh", "reload", "sync", "rotate", "cycle"],
  "🔄": ["counterclockwise arrows", "refresh", "reload", "reverse", "undo"],
  "🔙": ["back arrow", "return", "previous", "go back", "undo"],
  "🔚": ["end arrow", "finish", "last", "conclusion", "terminate"],
  "🔛": ["on arrow", "power", "activate", "enable", "start"],
  "🔜": ["soon arrow", "coming", "future", "upcoming", "next"],
  "🔝": ["top arrow", "highest", "best", "peak", "maximum", "up"],
  "🛐": ["place of worship", "pray", "religious", "temple", "spiritual"],
  "⚛️": ["atom", "science", "physics", "nuclear", "particle", "chemistry"],
  "🕉️": ["om", "hindu", "religion", "spiritual", "mantra", "sacred"],
  "✡️": [
    "star of david",
    "jewish",
    "judaism",
    "israel",
    "religion",
    "hexagram",
  ],
  "☸️": ["dharma wheel", "buddhism", "religion", "spiritual", "eight spokes"],
  "☯️": ["yin yang", "balance", "taoism", "opposites", "harmony", "chinese"],
  "✝️": ["latin cross", "christian", "religion", "church", "jesus", "faith"],
  "☦️": ["orthodox cross", "christian", "russian", "religion", "church"],
  "☪️": ["star and crescent", "islam", "muslim", "religion", "faith"],
  "☮️": ["peace", "peace symbol", "hippie", "pacifist", "anti-war"],
  "🕎": ["menorah", "jewish", "hanukkah", "candles", "judaism", "festival"],
  "🔯": ["dotted six-pointed star", "fortune", "hexagram", "mystical"],
  "♈": ["aries", "zodiac", "ram", "march", "april", "astrology", "horoscope"],
  "🔁": ["repeat", "loop", "again", "replay", "cycle"],
  "🔂": ["repeat single", "loop one", "replay", "music", "track"],
  "▶️": ["play", "start", "resume", "video", "music", "media"],
  "⏩": ["fast forward", "skip", "next", "speed", "advance"],
  "⏭️": ["next track", "skip", "forward", "music", "playlist"],
  "⏯️": ["play pause", "toggle", "media", "control", "music"],
  "◀️": ["reverse", "rewind", "back", "previous", "left"],
  "⏪": ["fast reverse", "rewind", "back", "speed", "previous"],
  "⏮️": ["previous track", "skip back", "music", "playlist"],
  "🔼": ["upwards button", "up", "increase", "arrow", "triangle"],
  "⏫": ["fast up", "double up", "increase", "maximum"],
  "🔽": ["downwards button", "down", "decrease", "arrow", "triangle"],
  "⏬": ["fast down", "double down", "decrease", "minimum"],
  "⏸️": ["pause", "stop", "hold", "wait", "media", "break"],
  "⏹️": ["stop", "square", "end", "halt", "media", "finish"],
  "⏺️": ["record", "capture", "red circle", "recording", "media"],
  "⏏️": ["eject", "remove", "disc", "media", "open"],
  "🎦": ["cinema", "movie", "film", "theater", "movies", "entertainment"],
  "🔅": ["dim button", "brightness low", "screen", "reduce", "darker"],
  "🔆": ["bright button", "brightness high", "screen", "increase", "lighter"],
  "📶": ["antenna bars", "signal", "wifi", "cellular", "strength", "network"],
  "📳": ["vibration mode", "phone", "silent", "buzz", "notification"],
  "📴": ["mobile phone off", "power off", "airplane mode", "offline"],

  // More can be added...
};

// Function to search for stickers based on emoji keywords
export function searchEmojiKeywords(query: string): string[] {
  const normalizedQuery = query.toLowerCase().trim();
  const matchingEmojis: string[] = [];

  // Search through all emoji mappings
  for (const [emoji, keywords] of Object.entries(emojiSearchMap)) {
    // Check if any keyword matches the query
    if (keywords.some((keyword) => keyword.includes(normalizedQuery))) {
      matchingEmojis.push(emoji);
    }
  }

  return matchingEmojis;
}

// Function to get keywords for a specific emoji
export function getEmojiKeywords(emoji: string): string[] {
  return emojiSearchMap[emoji] || [];
}

// Function to check if an emoji matches a search query
export function emojiMatchesQuery(emoji: string, query: string): boolean {
  const keywords = emojiSearchMap[emoji];
  if (!keywords) return false;

  const normalizedQuery = query.toLowerCase().trim();
  return keywords.some((keyword) => keyword.includes(normalizedQuery));
}

