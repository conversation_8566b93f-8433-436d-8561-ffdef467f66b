/**
 * This file exports all GIR module type definitions.
 */

import './glib-2.0.d.ts';
import './gio-2.0.d.ts';
import './gobject-2.0.d.ts';
import './xrandr-1.3.d.ts';
import './xlib-2.0.d.ts';
import './xft-2.0.d.ts';
import './xfixes-4.0.d.ts';
import './win32-1.0.d.ts';
import './libxml2-2.0.d.ts';
import './freetype2-2.0.d.ts';
import './fontconfig-2.0.d.ts';
import './cairo-1.0.d.ts';
import './xfconf-0.d.ts';
import './wp-0.5.d.ts';
import './webkitwebprocessextension-6.0.d.ts';
import './webkit-6.0.d.ts';
import './vulkan-1.0.d.ts';
import './vips-8.0.d.ts';
import './soup-3.0.d.ts';
import './shumate-1.0.d.ts';
import './rsvg-2.0.d.ts';
import './polkitagent-1.0.d.ts';
import './polkit-1.0.d.ts';
import './pangoxft-1.0.d.ts';
import './pangoot-1.0.d.ts';
import './pangofc-1.0.d.ts';
import './pangoft2-1.0.d.ts';
import './pangocairo-1.0.d.ts';
import './pango-1.0.d.ts';
import './nm-1.0.d.ts';
import './libxfce4util-1.0.d.ts';
import './libxfce4ui-2.0.d.ts';
import './libxfce4panel-2.0.d.ts';
import './json-1.0.d.ts';
import './javascriptcore-6.0.d.ts';
import './icalglib-3.0.d.ts';
import './ical-3.0.d.ts';
import './harfbuzz-0.0.d.ts';
import './gtklayershell-0.1.d.ts';
import './gtk4layershell-1.0.d.ts';
import './gtk-4.0.d.ts';
import './gtk-3.0.d.ts';
import './gstvideo-1.0.d.ts';
import './gsttag-1.0.d.ts';
import './gstsdp-1.0.d.ts';
import './gstrtsp-1.0.d.ts';
import './gstrtp-1.0.d.ts';
import './gstpbutils-1.0.d.ts';
import './gstnet-1.0.d.ts';
import './gstglx11-1.0.d.ts';
import './gstglwayland-1.0.d.ts';
import './gstglegl-1.0.d.ts';
import './gstgl-1.0.d.ts';
import './gstcontroller-1.0.d.ts';
import './gstcheck-1.0.d.ts';
import './gstbase-1.0.d.ts';
import './gstaudio-1.0.d.ts';
import './gstapp-1.0.d.ts';
import './gstallocators-1.0.d.ts';
import './gst-1.0.d.ts';
import './gsk-4.0.d.ts';
import './graphene-1.0.d.ts';
import './giounix-2.0.d.ts';
import './gdkx11-4.0.d.ts';
import './gdkx11-3.0.d.ts';
import './gdkwayland-4.0.d.ts';
import './gdkpixdata-2.0.d.ts';
import './gdkpixbuf-2.0.d.ts';
import './gdk-4.0.d.ts';
import './gdk-3.0.d.ts';
import './gcr-4.d.ts';
import './gck-2.d.ts';
import './garcongtk-1.0.d.ts';
import './garcon-1.0.d.ts';
import './gudev-1.0.d.ts';
import './gmodule-2.0.d.ts';
import './glibunix-2.0.d.ts';
import './gl-1.0.d.ts';
import './girepository-3.0.d.ts';
import './girepository-2.0.d.ts';
import './gdesktopenums-3.0.d.ts';
import './evinceview-3.0.d.ts';
import './evincedocument-3.0.d.ts';
import './dbusglib-1.0.d.ts';
import './dbus-1.0.d.ts';
import './cloudproviders-0.3.d.ts';
import './atspi-2.0.d.ts';
import './atk-1.0.d.ts';
import './astalwp-0.1.d.ts';
import './astaltray-0.1.d.ts';
import './astalriver-0.1.d.ts';
import './astalpowerprofiles-0.1.d.ts';
import './astalnotifd-0.1.d.ts';
import './astalnetwork-0.1.d.ts';
import './astalmpris-0.1.d.ts';
import './astalio-0.1.d.ts';
import './astalhyprland-0.1.d.ts';
import './astalgreet-0.1.d.ts';
import './astalbluetooth-0.1.d.ts';
import './astalbattery-0.1.d.ts';
import './astalauth-0.1.d.ts';
import './astalapps-0.1.d.ts';
import './astal-4.0.d.ts';
import './astal-3.0.d.ts';
import './appmenuglibtranslator-24.02.d.ts';
import './st-15.d.ts';
import './shell-15.d.ts';
import './gvc-1.0.d.ts';
import './shew-0.d.ts';
import './sushi-1.0.d.ts';
import './gc-1.0.d.ts';
import './gnomemaps-1.0.d.ts';
import './eog-3.0.d.ts';
import './mtk-15.d.ts';
import './metatest-15.d.ts';
import './meta-15.d.ts';
import './coglpango-15.d.ts';
import './cogl-15.d.ts';
import './clutter-15.d.ts';
