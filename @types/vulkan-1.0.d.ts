/// <reference path="./gobject-2.0.d.ts" />

/**
 * Type Definitions for Gjs (https://gjs.guide/)
 *
 * These type definitions are automatically generated, do not edit them by hand.
 * If you found a bug fix it in `ts-for-gir` or create a bug report on https://github.com/gjsify/ts-for-gir
 *
 * The based EJS template file is used for the generated .d.ts file of each GIR module like Gtk-4.0, GObject-2.0, ...
 */

declare module 'gi://Vulkan?version=1.0' {
    // Module dependencies
    import type GObject from 'gi://GObject?version=2.0';

    export namespace Vulkan {
        /**
         * Vulkan-1.0
         */

        class Bool32 {
            static $gtype: GObject.GType<Bool32>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceAddress {
            static $gtype: GObject.GType<DeviceAddress>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceSize {
            static $gtype: GObject.GType<DeviceSize>;

            // Constructors

            _init(...args: any[]): void;
        }

        class Flags {
            static $gtype: GObject.GType<Flags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SampleMask {
            static $gtype: GObject.GType<SampleMask>;

            // Constructors

            _init(...args: any[]): void;
        }

        class Buffer {
            static $gtype: GObject.GType<Buffer>;

            // Constructors

            _init(...args: any[]): void;
        }

        class Image {
            static $gtype: GObject.GType<Image>;

            // Constructors

            _init(...args: any[]): void;
        }

        class Instance {
            static $gtype: GObject.GType<Instance>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDevice {
            static $gtype: GObject.GType<PhysicalDevice>;

            // Constructors

            _init(...args: any[]): void;
        }

        class Device {
            static $gtype: GObject.GType<Device>;

            // Constructors

            _init(...args: any[]): void;
        }

        class Queue {
            static $gtype: GObject.GType<Queue>;

            // Constructors

            _init(...args: any[]): void;
        }

        class Semaphore {
            static $gtype: GObject.GType<Semaphore>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CommandBuffer {
            static $gtype: GObject.GType<CommandBuffer>;

            // Constructors

            _init(...args: any[]): void;
        }

        class Fence {
            static $gtype: GObject.GType<Fence>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceMemory {
            static $gtype: GObject.GType<DeviceMemory>;

            // Constructors

            _init(...args: any[]): void;
        }

        class Event {
            static $gtype: GObject.GType<Event>;

            // Constructors

            _init(...args: any[]): void;
        }

        class QueryPool {
            static $gtype: GObject.GType<QueryPool>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BufferView {
            static $gtype: GObject.GType<BufferView>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageView {
            static $gtype: GObject.GType<ImageView>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ShaderModule {
            static $gtype: GObject.GType<ShaderModule>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineCache {
            static $gtype: GObject.GType<PipelineCache>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineLayout {
            static $gtype: GObject.GType<PipelineLayout>;

            // Constructors

            _init(...args: any[]): void;
        }

        class Pipeline {
            static $gtype: GObject.GType<Pipeline>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RenderPass {
            static $gtype: GObject.GType<RenderPass>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorSetLayout {
            static $gtype: GObject.GType<DescriptorSetLayout>;

            // Constructors

            _init(...args: any[]): void;
        }

        class Sampler {
            static $gtype: GObject.GType<Sampler>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorSet {
            static $gtype: GObject.GType<DescriptorSet>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorPool {
            static $gtype: GObject.GType<DescriptorPool>;

            // Constructors

            _init(...args: any[]): void;
        }

        class Framebuffer {
            static $gtype: GObject.GType<Framebuffer>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CommandPool {
            static $gtype: GObject.GType<CommandPool>;

            // Constructors

            _init(...args: any[]): void;
        }

        class Result {
            static $gtype: GObject.GType<Result>;

            // Constructors

            _init(...args: any[]): void;
        }

        class StructureType {
            static $gtype: GObject.GType<StructureType>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineCacheHeaderVersion {
            static $gtype: GObject.GType<PipelineCacheHeaderVersion>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageLayout {
            static $gtype: GObject.GType<ImageLayout>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ObjectType {
            static $gtype: GObject.GType<ObjectType>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VendorId {
            static $gtype: GObject.GType<VendorId>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SystemAllocationScope {
            static $gtype: GObject.GType<SystemAllocationScope>;

            // Constructors

            _init(...args: any[]): void;
        }

        class InternalAllocationType {
            static $gtype: GObject.GType<InternalAllocationType>;

            // Constructors

            _init(...args: any[]): void;
        }

        class Format {
            static $gtype: GObject.GType<Format>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageTiling {
            static $gtype: GObject.GType<ImageTiling>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageType {
            static $gtype: GObject.GType<ImageType>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceType {
            static $gtype: GObject.GType<PhysicalDeviceType>;

            // Constructors

            _init(...args: any[]): void;
        }

        class QueryType {
            static $gtype: GObject.GType<QueryType>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SharingMode {
            static $gtype: GObject.GType<SharingMode>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ComponentSwizzle {
            static $gtype: GObject.GType<ComponentSwizzle>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageViewType {
            static $gtype: GObject.GType<ImageViewType>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BlendFactor {
            static $gtype: GObject.GType<BlendFactor>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BlendOp {
            static $gtype: GObject.GType<BlendOp>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CompareOp {
            static $gtype: GObject.GType<CompareOp>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DynamicState {
            static $gtype: GObject.GType<DynamicState>;

            // Constructors

            _init(...args: any[]): void;
        }

        class FrontFace {
            static $gtype: GObject.GType<FrontFace>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VertexInputRate {
            static $gtype: GObject.GType<VertexInputRate>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PrimitiveTopology {
            static $gtype: GObject.GType<PrimitiveTopology>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PolygonMode {
            static $gtype: GObject.GType<PolygonMode>;

            // Constructors

            _init(...args: any[]): void;
        }

        class StencilOp {
            static $gtype: GObject.GType<StencilOp>;

            // Constructors

            _init(...args: any[]): void;
        }

        class LogicOp {
            static $gtype: GObject.GType<LogicOp>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BorderColor {
            static $gtype: GObject.GType<BorderColor>;

            // Constructors

            _init(...args: any[]): void;
        }

        class Filter {
            static $gtype: GObject.GType<Filter>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SamplerAddressMode {
            static $gtype: GObject.GType<SamplerAddressMode>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SamplerMipmapMode {
            static $gtype: GObject.GType<SamplerMipmapMode>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorType {
            static $gtype: GObject.GType<DescriptorType>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AttachmentLoadOp {
            static $gtype: GObject.GType<AttachmentLoadOp>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AttachmentStoreOp {
            static $gtype: GObject.GType<AttachmentStoreOp>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineBindPoint {
            static $gtype: GObject.GType<PipelineBindPoint>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CommandBufferLevel {
            static $gtype: GObject.GType<CommandBufferLevel>;

            // Constructors

            _init(...args: any[]): void;
        }

        class IndexType {
            static $gtype: GObject.GType<IndexType>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SubpassContents {
            static $gtype: GObject.GType<SubpassContents>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AccessFlagBits {
            static $gtype: GObject.GType<AccessFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AccessFlags {
            static $gtype: GObject.GType<AccessFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageAspectFlagBits {
            static $gtype: GObject.GType<ImageAspectFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageAspectFlags {
            static $gtype: GObject.GType<ImageAspectFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class FormatFeatureFlagBits {
            static $gtype: GObject.GType<FormatFeatureFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class FormatFeatureFlags {
            static $gtype: GObject.GType<FormatFeatureFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageCreateFlagBits {
            static $gtype: GObject.GType<ImageCreateFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageCreateFlags {
            static $gtype: GObject.GType<ImageCreateFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SampleCountFlagBits {
            static $gtype: GObject.GType<SampleCountFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SampleCountFlags {
            static $gtype: GObject.GType<SampleCountFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageUsageFlagBits {
            static $gtype: GObject.GType<ImageUsageFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageUsageFlags {
            static $gtype: GObject.GType<ImageUsageFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class InstanceCreateFlagBits {
            static $gtype: GObject.GType<InstanceCreateFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class InstanceCreateFlags {
            static $gtype: GObject.GType<InstanceCreateFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MemoryHeapFlagBits {
            static $gtype: GObject.GType<MemoryHeapFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MemoryHeapFlags {
            static $gtype: GObject.GType<MemoryHeapFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MemoryPropertyFlagBits {
            static $gtype: GObject.GType<MemoryPropertyFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MemoryPropertyFlags {
            static $gtype: GObject.GType<MemoryPropertyFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class QueueFlagBits {
            static $gtype: GObject.GType<QueueFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class QueueFlags {
            static $gtype: GObject.GType<QueueFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceCreateFlags {
            static $gtype: GObject.GType<DeviceCreateFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceQueueCreateFlagBits {
            static $gtype: GObject.GType<DeviceQueueCreateFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceQueueCreateFlags {
            static $gtype: GObject.GType<DeviceQueueCreateFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineStageFlagBits {
            static $gtype: GObject.GType<PipelineStageFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineStageFlags {
            static $gtype: GObject.GType<PipelineStageFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MemoryMapFlagBits {
            static $gtype: GObject.GType<MemoryMapFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MemoryMapFlags {
            static $gtype: GObject.GType<MemoryMapFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SparseMemoryBindFlagBits {
            static $gtype: GObject.GType<SparseMemoryBindFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SparseMemoryBindFlags {
            static $gtype: GObject.GType<SparseMemoryBindFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SparseImageFormatFlagBits {
            static $gtype: GObject.GType<SparseImageFormatFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SparseImageFormatFlags {
            static $gtype: GObject.GType<SparseImageFormatFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class FenceCreateFlagBits {
            static $gtype: GObject.GType<FenceCreateFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class FenceCreateFlags {
            static $gtype: GObject.GType<FenceCreateFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SemaphoreCreateFlags {
            static $gtype: GObject.GType<SemaphoreCreateFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class EventCreateFlagBits {
            static $gtype: GObject.GType<EventCreateFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class EventCreateFlags {
            static $gtype: GObject.GType<EventCreateFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class QueryPipelineStatisticFlagBits {
            static $gtype: GObject.GType<QueryPipelineStatisticFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class QueryPipelineStatisticFlags {
            static $gtype: GObject.GType<QueryPipelineStatisticFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class QueryPoolCreateFlags {
            static $gtype: GObject.GType<QueryPoolCreateFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class QueryResultFlagBits {
            static $gtype: GObject.GType<QueryResultFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class QueryResultFlags {
            static $gtype: GObject.GType<QueryResultFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BufferCreateFlagBits {
            static $gtype: GObject.GType<BufferCreateFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BufferCreateFlags {
            static $gtype: GObject.GType<BufferCreateFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BufferUsageFlagBits {
            static $gtype: GObject.GType<BufferUsageFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BufferUsageFlags {
            static $gtype: GObject.GType<BufferUsageFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BufferViewCreateFlags {
            static $gtype: GObject.GType<BufferViewCreateFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageViewCreateFlagBits {
            static $gtype: GObject.GType<ImageViewCreateFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageViewCreateFlags {
            static $gtype: GObject.GType<ImageViewCreateFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ShaderModuleCreateFlags {
            static $gtype: GObject.GType<ShaderModuleCreateFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineCacheCreateFlagBits {
            static $gtype: GObject.GType<PipelineCacheCreateFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineCacheCreateFlags {
            static $gtype: GObject.GType<PipelineCacheCreateFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ColorComponentFlagBits {
            static $gtype: GObject.GType<ColorComponentFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ColorComponentFlags {
            static $gtype: GObject.GType<ColorComponentFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineCreateFlagBits {
            static $gtype: GObject.GType<PipelineCreateFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineCreateFlags {
            static $gtype: GObject.GType<PipelineCreateFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineShaderStageCreateFlagBits {
            static $gtype: GObject.GType<PipelineShaderStageCreateFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineShaderStageCreateFlags {
            static $gtype: GObject.GType<PipelineShaderStageCreateFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ShaderStageFlagBits {
            static $gtype: GObject.GType<ShaderStageFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CullModeFlagBits {
            static $gtype: GObject.GType<CullModeFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CullModeFlags {
            static $gtype: GObject.GType<CullModeFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineVertexInputStateCreateFlags {
            static $gtype: GObject.GType<PipelineVertexInputStateCreateFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineInputAssemblyStateCreateFlags {
            static $gtype: GObject.GType<PipelineInputAssemblyStateCreateFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineTessellationStateCreateFlags {
            static $gtype: GObject.GType<PipelineTessellationStateCreateFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineViewportStateCreateFlags {
            static $gtype: GObject.GType<PipelineViewportStateCreateFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineRasterizationStateCreateFlags {
            static $gtype: GObject.GType<PipelineRasterizationStateCreateFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineMultisampleStateCreateFlags {
            static $gtype: GObject.GType<PipelineMultisampleStateCreateFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineDepthStencilStateCreateFlagBits {
            static $gtype: GObject.GType<PipelineDepthStencilStateCreateFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineDepthStencilStateCreateFlags {
            static $gtype: GObject.GType<PipelineDepthStencilStateCreateFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineColorBlendStateCreateFlagBits {
            static $gtype: GObject.GType<PipelineColorBlendStateCreateFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineColorBlendStateCreateFlags {
            static $gtype: GObject.GType<PipelineColorBlendStateCreateFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineDynamicStateCreateFlags {
            static $gtype: GObject.GType<PipelineDynamicStateCreateFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineLayoutCreateFlagBits {
            static $gtype: GObject.GType<PipelineLayoutCreateFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineLayoutCreateFlags {
            static $gtype: GObject.GType<PipelineLayoutCreateFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ShaderStageFlags {
            static $gtype: GObject.GType<ShaderStageFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SamplerCreateFlagBits {
            static $gtype: GObject.GType<SamplerCreateFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SamplerCreateFlags {
            static $gtype: GObject.GType<SamplerCreateFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorPoolCreateFlagBits {
            static $gtype: GObject.GType<DescriptorPoolCreateFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorPoolCreateFlags {
            static $gtype: GObject.GType<DescriptorPoolCreateFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorPoolResetFlags {
            static $gtype: GObject.GType<DescriptorPoolResetFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorSetLayoutCreateFlagBits {
            static $gtype: GObject.GType<DescriptorSetLayoutCreateFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorSetLayoutCreateFlags {
            static $gtype: GObject.GType<DescriptorSetLayoutCreateFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AttachmentDescriptionFlagBits {
            static $gtype: GObject.GType<AttachmentDescriptionFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AttachmentDescriptionFlags {
            static $gtype: GObject.GType<AttachmentDescriptionFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DependencyFlagBits {
            static $gtype: GObject.GType<DependencyFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DependencyFlags {
            static $gtype: GObject.GType<DependencyFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class FramebufferCreateFlagBits {
            static $gtype: GObject.GType<FramebufferCreateFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class FramebufferCreateFlags {
            static $gtype: GObject.GType<FramebufferCreateFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RenderPassCreateFlagBits {
            static $gtype: GObject.GType<RenderPassCreateFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RenderPassCreateFlags {
            static $gtype: GObject.GType<RenderPassCreateFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SubpassDescriptionFlagBits {
            static $gtype: GObject.GType<SubpassDescriptionFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SubpassDescriptionFlags {
            static $gtype: GObject.GType<SubpassDescriptionFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CommandPoolCreateFlagBits {
            static $gtype: GObject.GType<CommandPoolCreateFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CommandPoolCreateFlags {
            static $gtype: GObject.GType<CommandPoolCreateFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CommandPoolResetFlagBits {
            static $gtype: GObject.GType<CommandPoolResetFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CommandPoolResetFlags {
            static $gtype: GObject.GType<CommandPoolResetFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CommandBufferUsageFlagBits {
            static $gtype: GObject.GType<CommandBufferUsageFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CommandBufferUsageFlags {
            static $gtype: GObject.GType<CommandBufferUsageFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class QueryControlFlagBits {
            static $gtype: GObject.GType<QueryControlFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class QueryControlFlags {
            static $gtype: GObject.GType<QueryControlFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CommandBufferResetFlagBits {
            static $gtype: GObject.GType<CommandBufferResetFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CommandBufferResetFlags {
            static $gtype: GObject.GType<CommandBufferResetFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class StencilFaceFlagBits {
            static $gtype: GObject.GType<StencilFaceFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class StencilFaceFlags {
            static $gtype: GObject.GType<StencilFaceFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class Extent2D {
            static $gtype: GObject.GType<Extent2D>;

            // Constructors

            _init(...args: any[]): void;
        }

        class Extent3D {
            static $gtype: GObject.GType<Extent3D>;

            // Constructors

            _init(...args: any[]): void;
        }

        class Offset2D {
            static $gtype: GObject.GType<Offset2D>;

            // Constructors

            _init(...args: any[]): void;
        }

        class Offset3D {
            static $gtype: GObject.GType<Offset3D>;

            // Constructors

            _init(...args: any[]): void;
        }

        class Rect2D {
            static $gtype: GObject.GType<Rect2D>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BaseInStructure {
            static $gtype: GObject.GType<BaseInStructure>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BaseOutStructure {
            static $gtype: GObject.GType<BaseOutStructure>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BufferMemoryBarrier {
            static $gtype: GObject.GType<BufferMemoryBarrier>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DispatchIndirectCommand {
            static $gtype: GObject.GType<DispatchIndirectCommand>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DrawIndexedIndirectCommand {
            static $gtype: GObject.GType<DrawIndexedIndirectCommand>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DrawIndirectCommand {
            static $gtype: GObject.GType<DrawIndirectCommand>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageSubresourceRange {
            static $gtype: GObject.GType<ImageSubresourceRange>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageMemoryBarrier {
            static $gtype: GObject.GType<ImageMemoryBarrier>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MemoryBarrier {
            static $gtype: GObject.GType<MemoryBarrier>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineCacheHeaderVersionOne {
            static $gtype: GObject.GType<PipelineCacheHeaderVersionOne>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AllocationCallbacks {
            static $gtype: GObject.GType<AllocationCallbacks>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ApplicationInfo {
            static $gtype: GObject.GType<ApplicationInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class FormatProperties {
            static $gtype: GObject.GType<FormatProperties>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageFormatProperties {
            static $gtype: GObject.GType<ImageFormatProperties>;

            // Constructors

            _init(...args: any[]): void;
        }

        class InstanceCreateInfo {
            static $gtype: GObject.GType<InstanceCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MemoryHeap {
            static $gtype: GObject.GType<MemoryHeap>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MemoryType {
            static $gtype: GObject.GType<MemoryType>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceFeatures {
            static $gtype: GObject.GType<PhysicalDeviceFeatures>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceLimits {
            static $gtype: GObject.GType<PhysicalDeviceLimits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceMemoryProperties {
            static $gtype: GObject.GType<PhysicalDeviceMemoryProperties>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceSparseProperties {
            static $gtype: GObject.GType<PhysicalDeviceSparseProperties>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceProperties {
            static $gtype: GObject.GType<PhysicalDeviceProperties>;

            // Constructors

            _init(...args: any[]): void;
        }

        class QueueFamilyProperties {
            static $gtype: GObject.GType<QueueFamilyProperties>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceQueueCreateInfo {
            static $gtype: GObject.GType<DeviceQueueCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceCreateInfo {
            static $gtype: GObject.GType<DeviceCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExtensionProperties {
            static $gtype: GObject.GType<ExtensionProperties>;

            // Constructors

            _init(...args: any[]): void;
        }

        class LayerProperties {
            static $gtype: GObject.GType<LayerProperties>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SubmitInfo {
            static $gtype: GObject.GType<SubmitInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MappedMemoryRange {
            static $gtype: GObject.GType<MappedMemoryRange>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MemoryAllocateInfo {
            static $gtype: GObject.GType<MemoryAllocateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MemoryRequirements {
            static $gtype: GObject.GType<MemoryRequirements>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SparseMemoryBind {
            static $gtype: GObject.GType<SparseMemoryBind>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SparseBufferMemoryBindInfo {
            static $gtype: GObject.GType<SparseBufferMemoryBindInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SparseImageOpaqueMemoryBindInfo {
            static $gtype: GObject.GType<SparseImageOpaqueMemoryBindInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageSubresource {
            static $gtype: GObject.GType<ImageSubresource>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SparseImageMemoryBind {
            static $gtype: GObject.GType<SparseImageMemoryBind>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SparseImageMemoryBindInfo {
            static $gtype: GObject.GType<SparseImageMemoryBindInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BindSparseInfo {
            static $gtype: GObject.GType<BindSparseInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SparseImageFormatProperties {
            static $gtype: GObject.GType<SparseImageFormatProperties>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SparseImageMemoryRequirements {
            static $gtype: GObject.GType<SparseImageMemoryRequirements>;

            // Constructors

            _init(...args: any[]): void;
        }

        class FenceCreateInfo {
            static $gtype: GObject.GType<FenceCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SemaphoreCreateInfo {
            static $gtype: GObject.GType<SemaphoreCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class EventCreateInfo {
            static $gtype: GObject.GType<EventCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class QueryPoolCreateInfo {
            static $gtype: GObject.GType<QueryPoolCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BufferCreateInfo {
            static $gtype: GObject.GType<BufferCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BufferViewCreateInfo {
            static $gtype: GObject.GType<BufferViewCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageCreateInfo {
            static $gtype: GObject.GType<ImageCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SubresourceLayout {
            static $gtype: GObject.GType<SubresourceLayout>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ComponentMapping {
            static $gtype: GObject.GType<ComponentMapping>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageViewCreateInfo {
            static $gtype: GObject.GType<ImageViewCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ShaderModuleCreateInfo {
            static $gtype: GObject.GType<ShaderModuleCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineCacheCreateInfo {
            static $gtype: GObject.GType<PipelineCacheCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SpecializationMapEntry {
            static $gtype: GObject.GType<SpecializationMapEntry>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SpecializationInfo {
            static $gtype: GObject.GType<SpecializationInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineShaderStageCreateInfo {
            static $gtype: GObject.GType<PipelineShaderStageCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ComputePipelineCreateInfo {
            static $gtype: GObject.GType<ComputePipelineCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VertexInputBindingDescription {
            static $gtype: GObject.GType<VertexInputBindingDescription>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VertexInputAttributeDescription {
            static $gtype: GObject.GType<VertexInputAttributeDescription>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineVertexInputStateCreateInfo {
            static $gtype: GObject.GType<PipelineVertexInputStateCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineInputAssemblyStateCreateInfo {
            static $gtype: GObject.GType<PipelineInputAssemblyStateCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineTessellationStateCreateInfo {
            static $gtype: GObject.GType<PipelineTessellationStateCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class Viewport {
            static $gtype: GObject.GType<Viewport>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineViewportStateCreateInfo {
            static $gtype: GObject.GType<PipelineViewportStateCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineRasterizationStateCreateInfo {
            static $gtype: GObject.GType<PipelineRasterizationStateCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineMultisampleStateCreateInfo {
            static $gtype: GObject.GType<PipelineMultisampleStateCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class StencilOpState {
            static $gtype: GObject.GType<StencilOpState>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineDepthStencilStateCreateInfo {
            static $gtype: GObject.GType<PipelineDepthStencilStateCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineColorBlendAttachmentState {
            static $gtype: GObject.GType<PipelineColorBlendAttachmentState>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineColorBlendStateCreateInfo {
            static $gtype: GObject.GType<PipelineColorBlendStateCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineDynamicStateCreateInfo {
            static $gtype: GObject.GType<PipelineDynamicStateCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class GraphicsPipelineCreateInfo {
            static $gtype: GObject.GType<GraphicsPipelineCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PushConstantRange {
            static $gtype: GObject.GType<PushConstantRange>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineLayoutCreateInfo {
            static $gtype: GObject.GType<PipelineLayoutCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SamplerCreateInfo {
            static $gtype: GObject.GType<SamplerCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CopyDescriptorSet {
            static $gtype: GObject.GType<CopyDescriptorSet>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorBufferInfo {
            static $gtype: GObject.GType<DescriptorBufferInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorImageInfo {
            static $gtype: GObject.GType<DescriptorImageInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorPoolSize {
            static $gtype: GObject.GType<DescriptorPoolSize>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorPoolCreateInfo {
            static $gtype: GObject.GType<DescriptorPoolCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorSetAllocateInfo {
            static $gtype: GObject.GType<DescriptorSetAllocateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorSetLayoutBinding {
            static $gtype: GObject.GType<DescriptorSetLayoutBinding>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorSetLayoutCreateInfo {
            static $gtype: GObject.GType<DescriptorSetLayoutCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class WriteDescriptorSet {
            static $gtype: GObject.GType<WriteDescriptorSet>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AttachmentDescription {
            static $gtype: GObject.GType<AttachmentDescription>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AttachmentReference {
            static $gtype: GObject.GType<AttachmentReference>;

            // Constructors

            _init(...args: any[]): void;
        }

        class FramebufferCreateInfo {
            static $gtype: GObject.GType<FramebufferCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SubpassDescription {
            static $gtype: GObject.GType<SubpassDescription>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SubpassDependency {
            static $gtype: GObject.GType<SubpassDependency>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RenderPassCreateInfo {
            static $gtype: GObject.GType<RenderPassCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CommandPoolCreateInfo {
            static $gtype: GObject.GType<CommandPoolCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CommandBufferAllocateInfo {
            static $gtype: GObject.GType<CommandBufferAllocateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CommandBufferInheritanceInfo {
            static $gtype: GObject.GType<CommandBufferInheritanceInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CommandBufferBeginInfo {
            static $gtype: GObject.GType<CommandBufferBeginInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BufferCopy {
            static $gtype: GObject.GType<BufferCopy>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageSubresourceLayers {
            static $gtype: GObject.GType<ImageSubresourceLayers>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BufferImageCopy {
            static $gtype: GObject.GType<BufferImageCopy>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ClearColorValue {
            static $gtype: GObject.GType<ClearColorValue>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ClearDepthStencilValue {
            static $gtype: GObject.GType<ClearDepthStencilValue>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ClearValue {
            static $gtype: GObject.GType<ClearValue>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ClearAttachment {
            static $gtype: GObject.GType<ClearAttachment>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ClearRect {
            static $gtype: GObject.GType<ClearRect>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageBlit {
            static $gtype: GObject.GType<ImageBlit>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageCopy {
            static $gtype: GObject.GType<ImageCopy>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageResolve {
            static $gtype: GObject.GType<ImageResolve>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RenderPassBeginInfo {
            static $gtype: GObject.GType<RenderPassBeginInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SamplerYcbcrConversion {
            static $gtype: GObject.GType<SamplerYcbcrConversion>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorUpdateTemplate {
            static $gtype: GObject.GType<DescriptorUpdateTemplate>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PointClippingBehavior {
            static $gtype: GObject.GType<PointClippingBehavior>;

            // Constructors

            _init(...args: any[]): void;
        }

        class TessellationDomainOrigin {
            static $gtype: GObject.GType<TessellationDomainOrigin>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SamplerYcbcrModelConversion {
            static $gtype: GObject.GType<SamplerYcbcrModelConversion>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SamplerYcbcrRange {
            static $gtype: GObject.GType<SamplerYcbcrRange>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ChromaLocation {
            static $gtype: GObject.GType<ChromaLocation>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorUpdateTemplateType {
            static $gtype: GObject.GType<DescriptorUpdateTemplateType>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SubgroupFeatureFlagBits {
            static $gtype: GObject.GType<SubgroupFeatureFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SubgroupFeatureFlags {
            static $gtype: GObject.GType<SubgroupFeatureFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PeerMemoryFeatureFlagBits {
            static $gtype: GObject.GType<PeerMemoryFeatureFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PeerMemoryFeatureFlags {
            static $gtype: GObject.GType<PeerMemoryFeatureFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MemoryAllocateFlagBits {
            static $gtype: GObject.GType<MemoryAllocateFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MemoryAllocateFlags {
            static $gtype: GObject.GType<MemoryAllocateFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CommandPoolTrimFlags {
            static $gtype: GObject.GType<CommandPoolTrimFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorUpdateTemplateCreateFlags {
            static $gtype: GObject.GType<DescriptorUpdateTemplateCreateFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalMemoryHandleTypeFlagBits {
            static $gtype: GObject.GType<ExternalMemoryHandleTypeFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalMemoryHandleTypeFlags {
            static $gtype: GObject.GType<ExternalMemoryHandleTypeFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalMemoryFeatureFlagBits {
            static $gtype: GObject.GType<ExternalMemoryFeatureFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalMemoryFeatureFlags {
            static $gtype: GObject.GType<ExternalMemoryFeatureFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalFenceHandleTypeFlagBits {
            static $gtype: GObject.GType<ExternalFenceHandleTypeFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalFenceHandleTypeFlags {
            static $gtype: GObject.GType<ExternalFenceHandleTypeFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalFenceFeatureFlagBits {
            static $gtype: GObject.GType<ExternalFenceFeatureFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalFenceFeatureFlags {
            static $gtype: GObject.GType<ExternalFenceFeatureFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class FenceImportFlagBits {
            static $gtype: GObject.GType<FenceImportFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class FenceImportFlags {
            static $gtype: GObject.GType<FenceImportFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SemaphoreImportFlagBits {
            static $gtype: GObject.GType<SemaphoreImportFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SemaphoreImportFlags {
            static $gtype: GObject.GType<SemaphoreImportFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalSemaphoreHandleTypeFlagBits {
            static $gtype: GObject.GType<ExternalSemaphoreHandleTypeFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalSemaphoreHandleTypeFlags {
            static $gtype: GObject.GType<ExternalSemaphoreHandleTypeFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalSemaphoreFeatureFlagBits {
            static $gtype: GObject.GType<ExternalSemaphoreFeatureFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalSemaphoreFeatureFlags {
            static $gtype: GObject.GType<ExternalSemaphoreFeatureFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceSubgroupProperties {
            static $gtype: GObject.GType<PhysicalDeviceSubgroupProperties>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BindBufferMemoryInfo {
            static $gtype: GObject.GType<BindBufferMemoryInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BindImageMemoryInfo {
            static $gtype: GObject.GType<BindImageMemoryInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDevice16BitStorageFeatures {
            static $gtype: GObject.GType<PhysicalDevice16BitStorageFeatures>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MemoryDedicatedRequirements {
            static $gtype: GObject.GType<MemoryDedicatedRequirements>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MemoryDedicatedAllocateInfo {
            static $gtype: GObject.GType<MemoryDedicatedAllocateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MemoryAllocateFlagsInfo {
            static $gtype: GObject.GType<MemoryAllocateFlagsInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceGroupRenderPassBeginInfo {
            static $gtype: GObject.GType<DeviceGroupRenderPassBeginInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceGroupCommandBufferBeginInfo {
            static $gtype: GObject.GType<DeviceGroupCommandBufferBeginInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceGroupSubmitInfo {
            static $gtype: GObject.GType<DeviceGroupSubmitInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceGroupBindSparseInfo {
            static $gtype: GObject.GType<DeviceGroupBindSparseInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BindBufferMemoryDeviceGroupInfo {
            static $gtype: GObject.GType<BindBufferMemoryDeviceGroupInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BindImageMemoryDeviceGroupInfo {
            static $gtype: GObject.GType<BindImageMemoryDeviceGroupInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceGroupProperties {
            static $gtype: GObject.GType<PhysicalDeviceGroupProperties>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceGroupDeviceCreateInfo {
            static $gtype: GObject.GType<DeviceGroupDeviceCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BufferMemoryRequirementsInfo2 {
            static $gtype: GObject.GType<BufferMemoryRequirementsInfo2>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageMemoryRequirementsInfo2 {
            static $gtype: GObject.GType<ImageMemoryRequirementsInfo2>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageSparseMemoryRequirementsInfo2 {
            static $gtype: GObject.GType<ImageSparseMemoryRequirementsInfo2>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MemoryRequirements2 {
            static $gtype: GObject.GType<MemoryRequirements2>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SparseImageMemoryRequirements2 {
            static $gtype: GObject.GType<SparseImageMemoryRequirements2>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceFeatures2 {
            static $gtype: GObject.GType<PhysicalDeviceFeatures2>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceProperties2 {
            static $gtype: GObject.GType<PhysicalDeviceProperties2>;

            // Constructors

            _init(...args: any[]): void;
        }

        class FormatProperties2 {
            static $gtype: GObject.GType<FormatProperties2>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageFormatProperties2 {
            static $gtype: GObject.GType<ImageFormatProperties2>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceImageFormatInfo2 {
            static $gtype: GObject.GType<PhysicalDeviceImageFormatInfo2>;

            // Constructors

            _init(...args: any[]): void;
        }

        class QueueFamilyProperties2 {
            static $gtype: GObject.GType<QueueFamilyProperties2>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceMemoryProperties2 {
            static $gtype: GObject.GType<PhysicalDeviceMemoryProperties2>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SparseImageFormatProperties2 {
            static $gtype: GObject.GType<SparseImageFormatProperties2>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceSparseImageFormatInfo2 {
            static $gtype: GObject.GType<PhysicalDeviceSparseImageFormatInfo2>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDevicePointClippingProperties {
            static $gtype: GObject.GType<PhysicalDevicePointClippingProperties>;

            // Constructors

            _init(...args: any[]): void;
        }

        class InputAttachmentAspectReference {
            static $gtype: GObject.GType<InputAttachmentAspectReference>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RenderPassInputAttachmentAspectCreateInfo {
            static $gtype: GObject.GType<RenderPassInputAttachmentAspectCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageViewUsageCreateInfo {
            static $gtype: GObject.GType<ImageViewUsageCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineTessellationDomainOriginStateCreateInfo {
            static $gtype: GObject.GType<PipelineTessellationDomainOriginStateCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RenderPassMultiviewCreateInfo {
            static $gtype: GObject.GType<RenderPassMultiviewCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceMultiviewFeatures {
            static $gtype: GObject.GType<PhysicalDeviceMultiviewFeatures>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceMultiviewProperties {
            static $gtype: GObject.GType<PhysicalDeviceMultiviewProperties>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceVariablePointersFeatures {
            static $gtype: GObject.GType<PhysicalDeviceVariablePointersFeatures>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceVariablePointerFeatures {
            static $gtype: GObject.GType<PhysicalDeviceVariablePointerFeatures>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceProtectedMemoryFeatures {
            static $gtype: GObject.GType<PhysicalDeviceProtectedMemoryFeatures>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceProtectedMemoryProperties {
            static $gtype: GObject.GType<PhysicalDeviceProtectedMemoryProperties>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceQueueInfo2 {
            static $gtype: GObject.GType<DeviceQueueInfo2>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ProtectedSubmitInfo {
            static $gtype: GObject.GType<ProtectedSubmitInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SamplerYcbcrConversionCreateInfo {
            static $gtype: GObject.GType<SamplerYcbcrConversionCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SamplerYcbcrConversionInfo {
            static $gtype: GObject.GType<SamplerYcbcrConversionInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BindImagePlaneMemoryInfo {
            static $gtype: GObject.GType<BindImagePlaneMemoryInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImagePlaneMemoryRequirementsInfo {
            static $gtype: GObject.GType<ImagePlaneMemoryRequirementsInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceSamplerYcbcrConversionFeatures {
            static $gtype: GObject.GType<PhysicalDeviceSamplerYcbcrConversionFeatures>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SamplerYcbcrConversionImageFormatProperties {
            static $gtype: GObject.GType<SamplerYcbcrConversionImageFormatProperties>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorUpdateTemplateEntry {
            static $gtype: GObject.GType<DescriptorUpdateTemplateEntry>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorUpdateTemplateCreateInfo {
            static $gtype: GObject.GType<DescriptorUpdateTemplateCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalMemoryProperties {
            static $gtype: GObject.GType<ExternalMemoryProperties>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceExternalImageFormatInfo {
            static $gtype: GObject.GType<PhysicalDeviceExternalImageFormatInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalImageFormatProperties {
            static $gtype: GObject.GType<ExternalImageFormatProperties>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceExternalBufferInfo {
            static $gtype: GObject.GType<PhysicalDeviceExternalBufferInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalBufferProperties {
            static $gtype: GObject.GType<ExternalBufferProperties>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceIDProperties {
            static $gtype: GObject.GType<PhysicalDeviceIDProperties>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalMemoryImageCreateInfo {
            static $gtype: GObject.GType<ExternalMemoryImageCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalMemoryBufferCreateInfo {
            static $gtype: GObject.GType<ExternalMemoryBufferCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExportMemoryAllocateInfo {
            static $gtype: GObject.GType<ExportMemoryAllocateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceExternalFenceInfo {
            static $gtype: GObject.GType<PhysicalDeviceExternalFenceInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalFenceProperties {
            static $gtype: GObject.GType<ExternalFenceProperties>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExportFenceCreateInfo {
            static $gtype: GObject.GType<ExportFenceCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExportSemaphoreCreateInfo {
            static $gtype: GObject.GType<ExportSemaphoreCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceExternalSemaphoreInfo {
            static $gtype: GObject.GType<PhysicalDeviceExternalSemaphoreInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalSemaphoreProperties {
            static $gtype: GObject.GType<ExternalSemaphoreProperties>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceMaintenance3Properties {
            static $gtype: GObject.GType<PhysicalDeviceMaintenance3Properties>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorSetLayoutSupport {
            static $gtype: GObject.GType<DescriptorSetLayoutSupport>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShaderDrawParametersFeatures {
            static $gtype: GObject.GType<PhysicalDeviceShaderDrawParametersFeatures>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShaderDrawParameterFeatures {
            static $gtype: GObject.GType<PhysicalDeviceShaderDrawParameterFeatures>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DriverId {
            static $gtype: GObject.GType<DriverId>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ShaderFloatControlsIndependence {
            static $gtype: GObject.GType<ShaderFloatControlsIndependence>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SamplerReductionMode {
            static $gtype: GObject.GType<SamplerReductionMode>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SemaphoreType {
            static $gtype: GObject.GType<SemaphoreType>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ResolveModeFlagBits {
            static $gtype: GObject.GType<ResolveModeFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ResolveModeFlags {
            static $gtype: GObject.GType<ResolveModeFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorBindingFlagBits {
            static $gtype: GObject.GType<DescriptorBindingFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorBindingFlags {
            static $gtype: GObject.GType<DescriptorBindingFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SemaphoreWaitFlagBits {
            static $gtype: GObject.GType<SemaphoreWaitFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SemaphoreWaitFlags {
            static $gtype: GObject.GType<SemaphoreWaitFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceVulkan11Features {
            static $gtype: GObject.GType<PhysicalDeviceVulkan11Features>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceVulkan11Properties {
            static $gtype: GObject.GType<PhysicalDeviceVulkan11Properties>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceVulkan12Features {
            static $gtype: GObject.GType<PhysicalDeviceVulkan12Features>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ConformanceVersion {
            static $gtype: GObject.GType<ConformanceVersion>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceVulkan12Properties {
            static $gtype: GObject.GType<PhysicalDeviceVulkan12Properties>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageFormatListCreateInfo {
            static $gtype: GObject.GType<ImageFormatListCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AttachmentDescription2 {
            static $gtype: GObject.GType<AttachmentDescription2>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AttachmentReference2 {
            static $gtype: GObject.GType<AttachmentReference2>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SubpassDescription2 {
            static $gtype: GObject.GType<SubpassDescription2>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SubpassDependency2 {
            static $gtype: GObject.GType<SubpassDependency2>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RenderPassCreateInfo2 {
            static $gtype: GObject.GType<RenderPassCreateInfo2>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SubpassBeginInfo {
            static $gtype: GObject.GType<SubpassBeginInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SubpassEndInfo {
            static $gtype: GObject.GType<SubpassEndInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDevice8BitStorageFeatures {
            static $gtype: GObject.GType<PhysicalDevice8BitStorageFeatures>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceDriverProperties {
            static $gtype: GObject.GType<PhysicalDeviceDriverProperties>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShaderAtomicInt64Features {
            static $gtype: GObject.GType<PhysicalDeviceShaderAtomicInt64Features>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShaderFloat16Int8Features {
            static $gtype: GObject.GType<PhysicalDeviceShaderFloat16Int8Features>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceFloatControlsProperties {
            static $gtype: GObject.GType<PhysicalDeviceFloatControlsProperties>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorSetLayoutBindingFlagsCreateInfo {
            static $gtype: GObject.GType<DescriptorSetLayoutBindingFlagsCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceDescriptorIndexingFeatures {
            static $gtype: GObject.GType<PhysicalDeviceDescriptorIndexingFeatures>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceDescriptorIndexingProperties {
            static $gtype: GObject.GType<PhysicalDeviceDescriptorIndexingProperties>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorSetVariableDescriptorCountAllocateInfo {
            static $gtype: GObject.GType<DescriptorSetVariableDescriptorCountAllocateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorSetVariableDescriptorCountLayoutSupport {
            static $gtype: GObject.GType<DescriptorSetVariableDescriptorCountLayoutSupport>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SubpassDescriptionDepthStencilResolve {
            static $gtype: GObject.GType<SubpassDescriptionDepthStencilResolve>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceDepthStencilResolveProperties {
            static $gtype: GObject.GType<PhysicalDeviceDepthStencilResolveProperties>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceScalarBlockLayoutFeatures {
            static $gtype: GObject.GType<PhysicalDeviceScalarBlockLayoutFeatures>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageStencilUsageCreateInfo {
            static $gtype: GObject.GType<ImageStencilUsageCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SamplerReductionModeCreateInfo {
            static $gtype: GObject.GType<SamplerReductionModeCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceSamplerFilterMinmaxProperties {
            static $gtype: GObject.GType<PhysicalDeviceSamplerFilterMinmaxProperties>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceVulkanMemoryModelFeatures {
            static $gtype: GObject.GType<PhysicalDeviceVulkanMemoryModelFeatures>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceImagelessFramebufferFeatures {
            static $gtype: GObject.GType<PhysicalDeviceImagelessFramebufferFeatures>;

            // Constructors

            _init(...args: any[]): void;
        }

        class FramebufferAttachmentImageInfo {
            static $gtype: GObject.GType<FramebufferAttachmentImageInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class FramebufferAttachmentsCreateInfo {
            static $gtype: GObject.GType<FramebufferAttachmentsCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RenderPassAttachmentBeginInfo {
            static $gtype: GObject.GType<RenderPassAttachmentBeginInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceUniformBufferStandardLayoutFeatures {
            static $gtype: GObject.GType<PhysicalDeviceUniformBufferStandardLayoutFeatures>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShaderSubgroupExtendedTypesFeatures {
            static $gtype: GObject.GType<PhysicalDeviceShaderSubgroupExtendedTypesFeatures>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceSeparateDepthStencilLayoutsFeatures {
            static $gtype: GObject.GType<PhysicalDeviceSeparateDepthStencilLayoutsFeatures>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AttachmentReferenceStencilLayout {
            static $gtype: GObject.GType<AttachmentReferenceStencilLayout>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AttachmentDescriptionStencilLayout {
            static $gtype: GObject.GType<AttachmentDescriptionStencilLayout>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceHostQueryResetFeatures {
            static $gtype: GObject.GType<PhysicalDeviceHostQueryResetFeatures>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceTimelineSemaphoreFeatures {
            static $gtype: GObject.GType<PhysicalDeviceTimelineSemaphoreFeatures>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceTimelineSemaphoreProperties {
            static $gtype: GObject.GType<PhysicalDeviceTimelineSemaphoreProperties>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SemaphoreTypeCreateInfo {
            static $gtype: GObject.GType<SemaphoreTypeCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class TimelineSemaphoreSubmitInfo {
            static $gtype: GObject.GType<TimelineSemaphoreSubmitInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SemaphoreWaitInfo {
            static $gtype: GObject.GType<SemaphoreWaitInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SemaphoreSignalInfo {
            static $gtype: GObject.GType<SemaphoreSignalInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceBufferDeviceAddressFeatures {
            static $gtype: GObject.GType<PhysicalDeviceBufferDeviceAddressFeatures>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BufferDeviceAddressInfo {
            static $gtype: GObject.GType<BufferDeviceAddressInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BufferOpaqueCaptureAddressCreateInfo {
            static $gtype: GObject.GType<BufferOpaqueCaptureAddressCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MemoryOpaqueCaptureAddressAllocateInfo {
            static $gtype: GObject.GType<MemoryOpaqueCaptureAddressAllocateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceMemoryOpaqueCaptureAddressInfo {
            static $gtype: GObject.GType<DeviceMemoryOpaqueCaptureAddressInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class Flags64 {
            static $gtype: GObject.GType<Flags64>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PrivateDataSlot {
            static $gtype: GObject.GType<PrivateDataSlot>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineCreationFeedbackFlagBits {
            static $gtype: GObject.GType<PipelineCreationFeedbackFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineCreationFeedbackFlags {
            static $gtype: GObject.GType<PipelineCreationFeedbackFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ToolPurposeFlagBits {
            static $gtype: GObject.GType<ToolPurposeFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ToolPurposeFlags {
            static $gtype: GObject.GType<ToolPurposeFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PrivateDataSlotCreateFlags {
            static $gtype: GObject.GType<PrivateDataSlotCreateFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineStageFlags2 {
            static $gtype: GObject.GType<PipelineStageFlags2>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineStageFlagBits2 {
            static $gtype: GObject.GType<PipelineStageFlagBits2>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AccessFlags2 {
            static $gtype: GObject.GType<AccessFlags2>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AccessFlagBits2 {
            static $gtype: GObject.GType<AccessFlagBits2>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SubmitFlagBits {
            static $gtype: GObject.GType<SubmitFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SubmitFlags {
            static $gtype: GObject.GType<SubmitFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RenderingFlagBits {
            static $gtype: GObject.GType<RenderingFlagBits>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RenderingFlags {
            static $gtype: GObject.GType<RenderingFlags>;

            // Constructors

            _init(...args: any[]): void;
        }

        class FormatFeatureFlags2 {
            static $gtype: GObject.GType<FormatFeatureFlags2>;

            // Constructors

            _init(...args: any[]): void;
        }

        class FormatFeatureFlagBits2 {
            static $gtype: GObject.GType<FormatFeatureFlagBits2>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceVulkan13Features {
            static $gtype: GObject.GType<PhysicalDeviceVulkan13Features>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceVulkan13Properties {
            static $gtype: GObject.GType<PhysicalDeviceVulkan13Properties>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineCreationFeedback {
            static $gtype: GObject.GType<PipelineCreationFeedback>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineCreationFeedbackCreateInfo {
            static $gtype: GObject.GType<PipelineCreationFeedbackCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShaderTerminateInvocationFeatures {
            static $gtype: GObject.GType<PhysicalDeviceShaderTerminateInvocationFeatures>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceToolProperties {
            static $gtype: GObject.GType<PhysicalDeviceToolProperties>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShaderDemoteToHelperInvocationFeatures {
            static $gtype: GObject.GType<PhysicalDeviceShaderDemoteToHelperInvocationFeatures>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDevicePrivateDataFeatures {
            static $gtype: GObject.GType<PhysicalDevicePrivateDataFeatures>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DevicePrivateDataCreateInfo {
            static $gtype: GObject.GType<DevicePrivateDataCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PrivateDataSlotCreateInfo {
            static $gtype: GObject.GType<PrivateDataSlotCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDevicePipelineCreationCacheControlFeatures {
            static $gtype: GObject.GType<PhysicalDevicePipelineCreationCacheControlFeatures>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MemoryBarrier2 {
            static $gtype: GObject.GType<MemoryBarrier2>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BufferMemoryBarrier2 {
            static $gtype: GObject.GType<BufferMemoryBarrier2>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageMemoryBarrier2 {
            static $gtype: GObject.GType<ImageMemoryBarrier2>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DependencyInfo {
            static $gtype: GObject.GType<DependencyInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SemaphoreSubmitInfo {
            static $gtype: GObject.GType<SemaphoreSubmitInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CommandBufferSubmitInfo {
            static $gtype: GObject.GType<CommandBufferSubmitInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SubmitInfo2 {
            static $gtype: GObject.GType<SubmitInfo2>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceSynchronization2Features {
            static $gtype: GObject.GType<PhysicalDeviceSynchronization2Features>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceZeroInitializeWorkgroupMemoryFeatures {
            static $gtype: GObject.GType<PhysicalDeviceZeroInitializeWorkgroupMemoryFeatures>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceImageRobustnessFeatures {
            static $gtype: GObject.GType<PhysicalDeviceImageRobustnessFeatures>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BufferCopy2 {
            static $gtype: GObject.GType<BufferCopy2>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CopyBufferInfo2 {
            static $gtype: GObject.GType<CopyBufferInfo2>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageCopy2 {
            static $gtype: GObject.GType<ImageCopy2>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CopyImageInfo2 {
            static $gtype: GObject.GType<CopyImageInfo2>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BufferImageCopy2 {
            static $gtype: GObject.GType<BufferImageCopy2>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CopyBufferToImageInfo2 {
            static $gtype: GObject.GType<CopyBufferToImageInfo2>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CopyImageToBufferInfo2 {
            static $gtype: GObject.GType<CopyImageToBufferInfo2>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageBlit2 {
            static $gtype: GObject.GType<ImageBlit2>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BlitImageInfo2 {
            static $gtype: GObject.GType<BlitImageInfo2>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageResolve2 {
            static $gtype: GObject.GType<ImageResolve2>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ResolveImageInfo2 {
            static $gtype: GObject.GType<ResolveImageInfo2>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceSubgroupSizeControlFeatures {
            static $gtype: GObject.GType<PhysicalDeviceSubgroupSizeControlFeatures>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceSubgroupSizeControlProperties {
            static $gtype: GObject.GType<PhysicalDeviceSubgroupSizeControlProperties>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineShaderStageRequiredSubgroupSizeCreateInfo {
            static $gtype: GObject.GType<PipelineShaderStageRequiredSubgroupSizeCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceInlineUniformBlockFeatures {
            static $gtype: GObject.GType<PhysicalDeviceInlineUniformBlockFeatures>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceInlineUniformBlockProperties {
            static $gtype: GObject.GType<PhysicalDeviceInlineUniformBlockProperties>;

            // Constructors

            _init(...args: any[]): void;
        }

        class WriteDescriptorSetInlineUniformBlock {
            static $gtype: GObject.GType<WriteDescriptorSetInlineUniformBlock>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorPoolInlineUniformBlockCreateInfo {
            static $gtype: GObject.GType<DescriptorPoolInlineUniformBlockCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceTextureCompressionASTCHDRFeatures {
            static $gtype: GObject.GType<PhysicalDeviceTextureCompressionASTCHDRFeatures>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RenderingAttachmentInfo {
            static $gtype: GObject.GType<RenderingAttachmentInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RenderingInfo {
            static $gtype: GObject.GType<RenderingInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineRenderingCreateInfo {
            static $gtype: GObject.GType<PipelineRenderingCreateInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceDynamicRenderingFeatures {
            static $gtype: GObject.GType<PhysicalDeviceDynamicRenderingFeatures>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CommandBufferInheritanceRenderingInfo {
            static $gtype: GObject.GType<CommandBufferInheritanceRenderingInfo>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShaderIntegerDotProductFeatures {
            static $gtype: GObject.GType<PhysicalDeviceShaderIntegerDotProductFeatures>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShaderIntegerDotProductProperties {
            static $gtype: GObject.GType<PhysicalDeviceShaderIntegerDotProductProperties>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceTexelBufferAlignmentProperties {
            static $gtype: GObject.GType<PhysicalDeviceTexelBufferAlignmentProperties>;

            // Constructors

            _init(...args: any[]): void;
        }

        class FormatProperties3 {
            static $gtype: GObject.GType<FormatProperties3>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceMaintenance4Features {
            static $gtype: GObject.GType<PhysicalDeviceMaintenance4Features>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceMaintenance4Properties {
            static $gtype: GObject.GType<PhysicalDeviceMaintenance4Properties>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceBufferMemoryRequirements {
            static $gtype: GObject.GType<DeviceBufferMemoryRequirements>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceImageMemoryRequirements {
            static $gtype: GObject.GType<DeviceImageMemoryRequirements>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SurfaceKHR {
            static $gtype: GObject.GType<SurfaceKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PresentModeKHR {
            static $gtype: GObject.GType<PresentModeKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ColorSpaceKHR {
            static $gtype: GObject.GType<ColorSpaceKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SurfaceTransformFlagBitsKHR {
            static $gtype: GObject.GType<SurfaceTransformFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CompositeAlphaFlagBitsKHR {
            static $gtype: GObject.GType<CompositeAlphaFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CompositeAlphaFlagsKHR {
            static $gtype: GObject.GType<CompositeAlphaFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SurfaceTransformFlagsKHR {
            static $gtype: GObject.GType<SurfaceTransformFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SurfaceCapabilitiesKHR {
            static $gtype: GObject.GType<SurfaceCapabilitiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SurfaceFormatKHR {
            static $gtype: GObject.GType<SurfaceFormatKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SwapchainKHR {
            static $gtype: GObject.GType<SwapchainKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SwapchainCreateFlagBitsKHR {
            static $gtype: GObject.GType<SwapchainCreateFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SwapchainCreateFlagsKHR {
            static $gtype: GObject.GType<SwapchainCreateFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceGroupPresentModeFlagBitsKHR {
            static $gtype: GObject.GType<DeviceGroupPresentModeFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceGroupPresentModeFlagsKHR {
            static $gtype: GObject.GType<DeviceGroupPresentModeFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SwapchainCreateInfoKHR {
            static $gtype: GObject.GType<SwapchainCreateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PresentInfoKHR {
            static $gtype: GObject.GType<PresentInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageSwapchainCreateInfoKHR {
            static $gtype: GObject.GType<ImageSwapchainCreateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BindImageMemorySwapchainInfoKHR {
            static $gtype: GObject.GType<BindImageMemorySwapchainInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AcquireNextImageInfoKHR {
            static $gtype: GObject.GType<AcquireNextImageInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceGroupPresentCapabilitiesKHR {
            static $gtype: GObject.GType<DeviceGroupPresentCapabilitiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceGroupPresentInfoKHR {
            static $gtype: GObject.GType<DeviceGroupPresentInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceGroupSwapchainCreateInfoKHR {
            static $gtype: GObject.GType<DeviceGroupSwapchainCreateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DisplayKHR {
            static $gtype: GObject.GType<DisplayKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DisplayModeKHR {
            static $gtype: GObject.GType<DisplayModeKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DisplayModeCreateFlagsKHR {
            static $gtype: GObject.GType<DisplayModeCreateFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DisplayPlaneAlphaFlagBitsKHR {
            static $gtype: GObject.GType<DisplayPlaneAlphaFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DisplayPlaneAlphaFlagsKHR {
            static $gtype: GObject.GType<DisplayPlaneAlphaFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DisplaySurfaceCreateFlagsKHR {
            static $gtype: GObject.GType<DisplaySurfaceCreateFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DisplayModeParametersKHR {
            static $gtype: GObject.GType<DisplayModeParametersKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DisplayModeCreateInfoKHR {
            static $gtype: GObject.GType<DisplayModeCreateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DisplayModePropertiesKHR {
            static $gtype: GObject.GType<DisplayModePropertiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DisplayPlaneCapabilitiesKHR {
            static $gtype: GObject.GType<DisplayPlaneCapabilitiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DisplayPlanePropertiesKHR {
            static $gtype: GObject.GType<DisplayPlanePropertiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DisplayPropertiesKHR {
            static $gtype: GObject.GType<DisplayPropertiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DisplaySurfaceCreateInfoKHR {
            static $gtype: GObject.GType<DisplaySurfaceCreateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DisplayPresentInfoKHR {
            static $gtype: GObject.GType<DisplayPresentInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoSessionKHR {
            static $gtype: GObject.GType<VideoSessionKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoSessionParametersKHR {
            static $gtype: GObject.GType<VideoSessionParametersKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class QueryResultStatusKHR {
            static $gtype: GObject.GType<QueryResultStatusKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoCodecOperationFlagBitsKHR {
            static $gtype: GObject.GType<VideoCodecOperationFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoCodecOperationFlagsKHR {
            static $gtype: GObject.GType<VideoCodecOperationFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoChromaSubsamplingFlagBitsKHR {
            static $gtype: GObject.GType<VideoChromaSubsamplingFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoChromaSubsamplingFlagsKHR {
            static $gtype: GObject.GType<VideoChromaSubsamplingFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoComponentBitDepthFlagBitsKHR {
            static $gtype: GObject.GType<VideoComponentBitDepthFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoComponentBitDepthFlagsKHR {
            static $gtype: GObject.GType<VideoComponentBitDepthFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoCapabilityFlagBitsKHR {
            static $gtype: GObject.GType<VideoCapabilityFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoCapabilityFlagsKHR {
            static $gtype: GObject.GType<VideoCapabilityFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoSessionCreateFlagBitsKHR {
            static $gtype: GObject.GType<VideoSessionCreateFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoSessionCreateFlagsKHR {
            static $gtype: GObject.GType<VideoSessionCreateFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoSessionParametersCreateFlagsKHR {
            static $gtype: GObject.GType<VideoSessionParametersCreateFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoBeginCodingFlagsKHR {
            static $gtype: GObject.GType<VideoBeginCodingFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEndCodingFlagsKHR {
            static $gtype: GObject.GType<VideoEndCodingFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoCodingControlFlagBitsKHR {
            static $gtype: GObject.GType<VideoCodingControlFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoCodingControlFlagsKHR {
            static $gtype: GObject.GType<VideoCodingControlFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class QueueFamilyQueryResultStatusPropertiesKHR {
            static $gtype: GObject.GType<QueueFamilyQueryResultStatusPropertiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class QueueFamilyVideoPropertiesKHR {
            static $gtype: GObject.GType<QueueFamilyVideoPropertiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoProfileInfoKHR {
            static $gtype: GObject.GType<VideoProfileInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoProfileListInfoKHR {
            static $gtype: GObject.GType<VideoProfileListInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoCapabilitiesKHR {
            static $gtype: GObject.GType<VideoCapabilitiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceVideoFormatInfoKHR {
            static $gtype: GObject.GType<PhysicalDeviceVideoFormatInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoFormatPropertiesKHR {
            static $gtype: GObject.GType<VideoFormatPropertiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoPictureResourceInfoKHR {
            static $gtype: GObject.GType<VideoPictureResourceInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoReferenceSlotInfoKHR {
            static $gtype: GObject.GType<VideoReferenceSlotInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoSessionMemoryRequirementsKHR {
            static $gtype: GObject.GType<VideoSessionMemoryRequirementsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BindVideoSessionMemoryInfoKHR {
            static $gtype: GObject.GType<BindVideoSessionMemoryInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoSessionCreateInfoKHR {
            static $gtype: GObject.GType<VideoSessionCreateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoSessionParametersCreateInfoKHR {
            static $gtype: GObject.GType<VideoSessionParametersCreateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoSessionParametersUpdateInfoKHR {
            static $gtype: GObject.GType<VideoSessionParametersUpdateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoBeginCodingInfoKHR {
            static $gtype: GObject.GType<VideoBeginCodingInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEndCodingInfoKHR {
            static $gtype: GObject.GType<VideoEndCodingInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoCodingControlInfoKHR {
            static $gtype: GObject.GType<VideoCodingControlInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoDecodeCapabilityFlagBitsKHR {
            static $gtype: GObject.GType<VideoDecodeCapabilityFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoDecodeCapabilityFlagsKHR {
            static $gtype: GObject.GType<VideoDecodeCapabilityFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoDecodeUsageFlagBitsKHR {
            static $gtype: GObject.GType<VideoDecodeUsageFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoDecodeUsageFlagsKHR {
            static $gtype: GObject.GType<VideoDecodeUsageFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoDecodeFlagsKHR {
            static $gtype: GObject.GType<VideoDecodeFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoDecodeCapabilitiesKHR {
            static $gtype: GObject.GType<VideoDecodeCapabilitiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoDecodeUsageInfoKHR {
            static $gtype: GObject.GType<VideoDecodeUsageInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoDecodeInfoKHR {
            static $gtype: GObject.GType<VideoDecodeInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH264CapabilityFlagBitsKHR {
            static $gtype: GObject.GType<VideoEncodeH264CapabilityFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH264CapabilityFlagsKHR {
            static $gtype: GObject.GType<VideoEncodeH264CapabilityFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH264StdFlagBitsKHR {
            static $gtype: GObject.GType<VideoEncodeH264StdFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH264StdFlagsKHR {
            static $gtype: GObject.GType<VideoEncodeH264StdFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH264RateControlFlagBitsKHR {
            static $gtype: GObject.GType<VideoEncodeH264RateControlFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH264RateControlFlagsKHR {
            static $gtype: GObject.GType<VideoEncodeH264RateControlFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH264CapabilitiesKHR {
            static $gtype: GObject.GType<VideoEncodeH264CapabilitiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH264QpKHR {
            static $gtype: GObject.GType<VideoEncodeH264QpKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH264QualityLevelPropertiesKHR {
            static $gtype: GObject.GType<VideoEncodeH264QualityLevelPropertiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH264SessionCreateInfoKHR {
            static $gtype: GObject.GType<VideoEncodeH264SessionCreateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH264SessionParametersAddInfoKHR {
            static $gtype: GObject.GType<VideoEncodeH264SessionParametersAddInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH264SessionParametersCreateInfoKHR {
            static $gtype: GObject.GType<VideoEncodeH264SessionParametersCreateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH264SessionParametersGetInfoKHR {
            static $gtype: GObject.GType<VideoEncodeH264SessionParametersGetInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH264SessionParametersFeedbackInfoKHR {
            static $gtype: GObject.GType<VideoEncodeH264SessionParametersFeedbackInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH264NaluSliceInfoKHR {
            static $gtype: GObject.GType<VideoEncodeH264NaluSliceInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH264PictureInfoKHR {
            static $gtype: GObject.GType<VideoEncodeH264PictureInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH264DpbSlotInfoKHR {
            static $gtype: GObject.GType<VideoEncodeH264DpbSlotInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH264ProfileInfoKHR {
            static $gtype: GObject.GType<VideoEncodeH264ProfileInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH264RateControlInfoKHR {
            static $gtype: GObject.GType<VideoEncodeH264RateControlInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH264FrameSizeKHR {
            static $gtype: GObject.GType<VideoEncodeH264FrameSizeKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH264RateControlLayerInfoKHR {
            static $gtype: GObject.GType<VideoEncodeH264RateControlLayerInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH264GopRemainingFrameInfoKHR {
            static $gtype: GObject.GType<VideoEncodeH264GopRemainingFrameInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH265CapabilityFlagBitsKHR {
            static $gtype: GObject.GType<VideoEncodeH265CapabilityFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH265CapabilityFlagsKHR {
            static $gtype: GObject.GType<VideoEncodeH265CapabilityFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH265StdFlagBitsKHR {
            static $gtype: GObject.GType<VideoEncodeH265StdFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH265StdFlagsKHR {
            static $gtype: GObject.GType<VideoEncodeH265StdFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH265CtbSizeFlagBitsKHR {
            static $gtype: GObject.GType<VideoEncodeH265CtbSizeFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH265CtbSizeFlagsKHR {
            static $gtype: GObject.GType<VideoEncodeH265CtbSizeFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH265TransformBlockSizeFlagBitsKHR {
            static $gtype: GObject.GType<VideoEncodeH265TransformBlockSizeFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH265TransformBlockSizeFlagsKHR {
            static $gtype: GObject.GType<VideoEncodeH265TransformBlockSizeFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH265RateControlFlagBitsKHR {
            static $gtype: GObject.GType<VideoEncodeH265RateControlFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH265RateControlFlagsKHR {
            static $gtype: GObject.GType<VideoEncodeH265RateControlFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH265CapabilitiesKHR {
            static $gtype: GObject.GType<VideoEncodeH265CapabilitiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH265SessionCreateInfoKHR {
            static $gtype: GObject.GType<VideoEncodeH265SessionCreateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH265QpKHR {
            static $gtype: GObject.GType<VideoEncodeH265QpKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH265QualityLevelPropertiesKHR {
            static $gtype: GObject.GType<VideoEncodeH265QualityLevelPropertiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH265SessionParametersAddInfoKHR {
            static $gtype: GObject.GType<VideoEncodeH265SessionParametersAddInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH265SessionParametersCreateInfoKHR {
            static $gtype: GObject.GType<VideoEncodeH265SessionParametersCreateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH265SessionParametersGetInfoKHR {
            static $gtype: GObject.GType<VideoEncodeH265SessionParametersGetInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH265SessionParametersFeedbackInfoKHR {
            static $gtype: GObject.GType<VideoEncodeH265SessionParametersFeedbackInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH265NaluSliceSegmentInfoKHR {
            static $gtype: GObject.GType<VideoEncodeH265NaluSliceSegmentInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH265PictureInfoKHR {
            static $gtype: GObject.GType<VideoEncodeH265PictureInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH265DpbSlotInfoKHR {
            static $gtype: GObject.GType<VideoEncodeH265DpbSlotInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH265ProfileInfoKHR {
            static $gtype: GObject.GType<VideoEncodeH265ProfileInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH265RateControlInfoKHR {
            static $gtype: GObject.GType<VideoEncodeH265RateControlInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH265FrameSizeKHR {
            static $gtype: GObject.GType<VideoEncodeH265FrameSizeKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH265RateControlLayerInfoKHR {
            static $gtype: GObject.GType<VideoEncodeH265RateControlLayerInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeH265GopRemainingFrameInfoKHR {
            static $gtype: GObject.GType<VideoEncodeH265GopRemainingFrameInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoDecodeH264PictureLayoutFlagBitsKHR {
            static $gtype: GObject.GType<VideoDecodeH264PictureLayoutFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoDecodeH264PictureLayoutFlagsKHR {
            static $gtype: GObject.GType<VideoDecodeH264PictureLayoutFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoDecodeH264ProfileInfoKHR {
            static $gtype: GObject.GType<VideoDecodeH264ProfileInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoDecodeH264CapabilitiesKHR {
            static $gtype: GObject.GType<VideoDecodeH264CapabilitiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoDecodeH264SessionParametersAddInfoKHR {
            static $gtype: GObject.GType<VideoDecodeH264SessionParametersAddInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoDecodeH264SessionParametersCreateInfoKHR {
            static $gtype: GObject.GType<VideoDecodeH264SessionParametersCreateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoDecodeH264PictureInfoKHR {
            static $gtype: GObject.GType<VideoDecodeH264PictureInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoDecodeH264DpbSlotInfoKHR {
            static $gtype: GObject.GType<VideoDecodeH264DpbSlotInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RenderingFlagsKHR {
            static $gtype: GObject.GType<RenderingFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RenderingFlagBitsKHR {
            static $gtype: GObject.GType<RenderingFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RenderingInfoKHR {
            static $gtype: GObject.GType<RenderingInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RenderingAttachmentInfoKHR {
            static $gtype: GObject.GType<RenderingAttachmentInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineRenderingCreateInfoKHR {
            static $gtype: GObject.GType<PipelineRenderingCreateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceDynamicRenderingFeaturesKHR {
            static $gtype: GObject.GType<PhysicalDeviceDynamicRenderingFeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CommandBufferInheritanceRenderingInfoKHR {
            static $gtype: GObject.GType<CommandBufferInheritanceRenderingInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RenderingFragmentShadingRateAttachmentInfoKHR {
            static $gtype: GObject.GType<RenderingFragmentShadingRateAttachmentInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RenderingFragmentDensityMapAttachmentInfoEXT {
            static $gtype: GObject.GType<RenderingFragmentDensityMapAttachmentInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AttachmentSampleCountInfoAMD {
            static $gtype: GObject.GType<AttachmentSampleCountInfoAMD>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AttachmentSampleCountInfoNV {
            static $gtype: GObject.GType<AttachmentSampleCountInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MultiviewPerViewAttributesInfoNVX {
            static $gtype: GObject.GType<MultiviewPerViewAttributesInfoNVX>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RenderPassMultiviewCreateInfoKHR {
            static $gtype: GObject.GType<RenderPassMultiviewCreateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceMultiviewFeaturesKHR {
            static $gtype: GObject.GType<PhysicalDeviceMultiviewFeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceMultiviewPropertiesKHR {
            static $gtype: GObject.GType<PhysicalDeviceMultiviewPropertiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceFeatures2KHR {
            static $gtype: GObject.GType<PhysicalDeviceFeatures2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceProperties2KHR {
            static $gtype: GObject.GType<PhysicalDeviceProperties2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class FormatProperties2KHR {
            static $gtype: GObject.GType<FormatProperties2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageFormatProperties2KHR {
            static $gtype: GObject.GType<ImageFormatProperties2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceImageFormatInfo2KHR {
            static $gtype: GObject.GType<PhysicalDeviceImageFormatInfo2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class QueueFamilyProperties2KHR {
            static $gtype: GObject.GType<QueueFamilyProperties2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceMemoryProperties2KHR {
            static $gtype: GObject.GType<PhysicalDeviceMemoryProperties2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SparseImageFormatProperties2KHR {
            static $gtype: GObject.GType<SparseImageFormatProperties2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceSparseImageFormatInfo2KHR {
            static $gtype: GObject.GType<PhysicalDeviceSparseImageFormatInfo2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PeerMemoryFeatureFlagsKHR {
            static $gtype: GObject.GType<PeerMemoryFeatureFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PeerMemoryFeatureFlagBitsKHR {
            static $gtype: GObject.GType<PeerMemoryFeatureFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MemoryAllocateFlagsKHR {
            static $gtype: GObject.GType<MemoryAllocateFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MemoryAllocateFlagBitsKHR {
            static $gtype: GObject.GType<MemoryAllocateFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MemoryAllocateFlagsInfoKHR {
            static $gtype: GObject.GType<MemoryAllocateFlagsInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceGroupRenderPassBeginInfoKHR {
            static $gtype: GObject.GType<DeviceGroupRenderPassBeginInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceGroupCommandBufferBeginInfoKHR {
            static $gtype: GObject.GType<DeviceGroupCommandBufferBeginInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceGroupSubmitInfoKHR {
            static $gtype: GObject.GType<DeviceGroupSubmitInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceGroupBindSparseInfoKHR {
            static $gtype: GObject.GType<DeviceGroupBindSparseInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BindBufferMemoryDeviceGroupInfoKHR {
            static $gtype: GObject.GType<BindBufferMemoryDeviceGroupInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BindImageMemoryDeviceGroupInfoKHR {
            static $gtype: GObject.GType<BindImageMemoryDeviceGroupInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CommandPoolTrimFlagsKHR {
            static $gtype: GObject.GType<CommandPoolTrimFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceGroupPropertiesKHR {
            static $gtype: GObject.GType<PhysicalDeviceGroupPropertiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceGroupDeviceCreateInfoKHR {
            static $gtype: GObject.GType<DeviceGroupDeviceCreateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalMemoryHandleTypeFlagsKHR {
            static $gtype: GObject.GType<ExternalMemoryHandleTypeFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalMemoryHandleTypeFlagBitsKHR {
            static $gtype: GObject.GType<ExternalMemoryHandleTypeFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalMemoryFeatureFlagsKHR {
            static $gtype: GObject.GType<ExternalMemoryFeatureFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalMemoryFeatureFlagBitsKHR {
            static $gtype: GObject.GType<ExternalMemoryFeatureFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalMemoryPropertiesKHR {
            static $gtype: GObject.GType<ExternalMemoryPropertiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceExternalImageFormatInfoKHR {
            static $gtype: GObject.GType<PhysicalDeviceExternalImageFormatInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalImageFormatPropertiesKHR {
            static $gtype: GObject.GType<ExternalImageFormatPropertiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceExternalBufferInfoKHR {
            static $gtype: GObject.GType<PhysicalDeviceExternalBufferInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalBufferPropertiesKHR {
            static $gtype: GObject.GType<ExternalBufferPropertiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceIDPropertiesKHR {
            static $gtype: GObject.GType<PhysicalDeviceIDPropertiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalMemoryImageCreateInfoKHR {
            static $gtype: GObject.GType<ExternalMemoryImageCreateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalMemoryBufferCreateInfoKHR {
            static $gtype: GObject.GType<ExternalMemoryBufferCreateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExportMemoryAllocateInfoKHR {
            static $gtype: GObject.GType<ExportMemoryAllocateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImportMemoryFdInfoKHR {
            static $gtype: GObject.GType<ImportMemoryFdInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MemoryFdPropertiesKHR {
            static $gtype: GObject.GType<MemoryFdPropertiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MemoryGetFdInfoKHR {
            static $gtype: GObject.GType<MemoryGetFdInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalSemaphoreHandleTypeFlagsKHR {
            static $gtype: GObject.GType<ExternalSemaphoreHandleTypeFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalSemaphoreHandleTypeFlagBitsKHR {
            static $gtype: GObject.GType<ExternalSemaphoreHandleTypeFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalSemaphoreFeatureFlagsKHR {
            static $gtype: GObject.GType<ExternalSemaphoreFeatureFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalSemaphoreFeatureFlagBitsKHR {
            static $gtype: GObject.GType<ExternalSemaphoreFeatureFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceExternalSemaphoreInfoKHR {
            static $gtype: GObject.GType<PhysicalDeviceExternalSemaphoreInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalSemaphorePropertiesKHR {
            static $gtype: GObject.GType<ExternalSemaphorePropertiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SemaphoreImportFlagsKHR {
            static $gtype: GObject.GType<SemaphoreImportFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SemaphoreImportFlagBitsKHR {
            static $gtype: GObject.GType<SemaphoreImportFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExportSemaphoreCreateInfoKHR {
            static $gtype: GObject.GType<ExportSemaphoreCreateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImportSemaphoreFdInfoKHR {
            static $gtype: GObject.GType<ImportSemaphoreFdInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SemaphoreGetFdInfoKHR {
            static $gtype: GObject.GType<SemaphoreGetFdInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDevicePushDescriptorPropertiesKHR {
            static $gtype: GObject.GType<PhysicalDevicePushDescriptorPropertiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShaderFloat16Int8FeaturesKHR {
            static $gtype: GObject.GType<PhysicalDeviceShaderFloat16Int8FeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceFloat16Int8FeaturesKHR {
            static $gtype: GObject.GType<PhysicalDeviceFloat16Int8FeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDevice16BitStorageFeaturesKHR {
            static $gtype: GObject.GType<PhysicalDevice16BitStorageFeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RectLayerKHR {
            static $gtype: GObject.GType<RectLayerKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PresentRegionKHR {
            static $gtype: GObject.GType<PresentRegionKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PresentRegionsKHR {
            static $gtype: GObject.GType<PresentRegionsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorUpdateTemplateKHR {
            static $gtype: GObject.GType<DescriptorUpdateTemplateKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorUpdateTemplateTypeKHR {
            static $gtype: GObject.GType<DescriptorUpdateTemplateTypeKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorUpdateTemplateCreateFlagsKHR {
            static $gtype: GObject.GType<DescriptorUpdateTemplateCreateFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorUpdateTemplateEntryKHR {
            static $gtype: GObject.GType<DescriptorUpdateTemplateEntryKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorUpdateTemplateCreateInfoKHR {
            static $gtype: GObject.GType<DescriptorUpdateTemplateCreateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceImagelessFramebufferFeaturesKHR {
            static $gtype: GObject.GType<PhysicalDeviceImagelessFramebufferFeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class FramebufferAttachmentsCreateInfoKHR {
            static $gtype: GObject.GType<FramebufferAttachmentsCreateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class FramebufferAttachmentImageInfoKHR {
            static $gtype: GObject.GType<FramebufferAttachmentImageInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RenderPassAttachmentBeginInfoKHR {
            static $gtype: GObject.GType<RenderPassAttachmentBeginInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RenderPassCreateInfo2KHR {
            static $gtype: GObject.GType<RenderPassCreateInfo2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AttachmentDescription2KHR {
            static $gtype: GObject.GType<AttachmentDescription2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AttachmentReference2KHR {
            static $gtype: GObject.GType<AttachmentReference2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SubpassDescription2KHR {
            static $gtype: GObject.GType<SubpassDescription2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SubpassDependency2KHR {
            static $gtype: GObject.GType<SubpassDependency2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SubpassBeginInfoKHR {
            static $gtype: GObject.GType<SubpassBeginInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SubpassEndInfoKHR {
            static $gtype: GObject.GType<SubpassEndInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SharedPresentSurfaceCapabilitiesKHR {
            static $gtype: GObject.GType<SharedPresentSurfaceCapabilitiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalFenceHandleTypeFlagsKHR {
            static $gtype: GObject.GType<ExternalFenceHandleTypeFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalFenceHandleTypeFlagBitsKHR {
            static $gtype: GObject.GType<ExternalFenceHandleTypeFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalFenceFeatureFlagsKHR {
            static $gtype: GObject.GType<ExternalFenceFeatureFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalFenceFeatureFlagBitsKHR {
            static $gtype: GObject.GType<ExternalFenceFeatureFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceExternalFenceInfoKHR {
            static $gtype: GObject.GType<PhysicalDeviceExternalFenceInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalFencePropertiesKHR {
            static $gtype: GObject.GType<ExternalFencePropertiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class FenceImportFlagsKHR {
            static $gtype: GObject.GType<FenceImportFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class FenceImportFlagBitsKHR {
            static $gtype: GObject.GType<FenceImportFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExportFenceCreateInfoKHR {
            static $gtype: GObject.GType<ExportFenceCreateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImportFenceFdInfoKHR {
            static $gtype: GObject.GType<ImportFenceFdInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class FenceGetFdInfoKHR {
            static $gtype: GObject.GType<FenceGetFdInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PerformanceCounterUnitKHR {
            static $gtype: GObject.GType<PerformanceCounterUnitKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PerformanceCounterScopeKHR {
            static $gtype: GObject.GType<PerformanceCounterScopeKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PerformanceCounterStorageKHR {
            static $gtype: GObject.GType<PerformanceCounterStorageKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PerformanceCounterDescriptionFlagBitsKHR {
            static $gtype: GObject.GType<PerformanceCounterDescriptionFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PerformanceCounterDescriptionFlagsKHR {
            static $gtype: GObject.GType<PerformanceCounterDescriptionFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AcquireProfilingLockFlagBitsKHR {
            static $gtype: GObject.GType<AcquireProfilingLockFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AcquireProfilingLockFlagsKHR {
            static $gtype: GObject.GType<AcquireProfilingLockFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDevicePerformanceQueryFeaturesKHR {
            static $gtype: GObject.GType<PhysicalDevicePerformanceQueryFeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDevicePerformanceQueryPropertiesKHR {
            static $gtype: GObject.GType<PhysicalDevicePerformanceQueryPropertiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PerformanceCounterKHR {
            static $gtype: GObject.GType<PerformanceCounterKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PerformanceCounterDescriptionKHR {
            static $gtype: GObject.GType<PerformanceCounterDescriptionKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class QueryPoolPerformanceCreateInfoKHR {
            static $gtype: GObject.GType<QueryPoolPerformanceCreateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PerformanceCounterResultKHR {
            static $gtype: GObject.GType<PerformanceCounterResultKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AcquireProfilingLockInfoKHR {
            static $gtype: GObject.GType<AcquireProfilingLockInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PerformanceQuerySubmitInfoKHR {
            static $gtype: GObject.GType<PerformanceQuerySubmitInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PointClippingBehaviorKHR {
            static $gtype: GObject.GType<PointClippingBehaviorKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class TessellationDomainOriginKHR {
            static $gtype: GObject.GType<TessellationDomainOriginKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDevicePointClippingPropertiesKHR {
            static $gtype: GObject.GType<PhysicalDevicePointClippingPropertiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RenderPassInputAttachmentAspectCreateInfoKHR {
            static $gtype: GObject.GType<RenderPassInputAttachmentAspectCreateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class InputAttachmentAspectReferenceKHR {
            static $gtype: GObject.GType<InputAttachmentAspectReferenceKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageViewUsageCreateInfoKHR {
            static $gtype: GObject.GType<ImageViewUsageCreateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineTessellationDomainOriginStateCreateInfoKHR {
            static $gtype: GObject.GType<PipelineTessellationDomainOriginStateCreateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceSurfaceInfo2KHR {
            static $gtype: GObject.GType<PhysicalDeviceSurfaceInfo2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SurfaceCapabilities2KHR {
            static $gtype: GObject.GType<SurfaceCapabilities2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SurfaceFormat2KHR {
            static $gtype: GObject.GType<SurfaceFormat2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceVariablePointerFeaturesKHR {
            static $gtype: GObject.GType<PhysicalDeviceVariablePointerFeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceVariablePointersFeaturesKHR {
            static $gtype: GObject.GType<PhysicalDeviceVariablePointersFeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DisplayProperties2KHR {
            static $gtype: GObject.GType<DisplayProperties2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DisplayPlaneProperties2KHR {
            static $gtype: GObject.GType<DisplayPlaneProperties2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DisplayModeProperties2KHR {
            static $gtype: GObject.GType<DisplayModeProperties2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DisplayPlaneInfo2KHR {
            static $gtype: GObject.GType<DisplayPlaneInfo2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DisplayPlaneCapabilities2KHR {
            static $gtype: GObject.GType<DisplayPlaneCapabilities2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MemoryDedicatedRequirementsKHR {
            static $gtype: GObject.GType<MemoryDedicatedRequirementsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MemoryDedicatedAllocateInfoKHR {
            static $gtype: GObject.GType<MemoryDedicatedAllocateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BufferMemoryRequirementsInfo2KHR {
            static $gtype: GObject.GType<BufferMemoryRequirementsInfo2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageMemoryRequirementsInfo2KHR {
            static $gtype: GObject.GType<ImageMemoryRequirementsInfo2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageSparseMemoryRequirementsInfo2KHR {
            static $gtype: GObject.GType<ImageSparseMemoryRequirementsInfo2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MemoryRequirements2KHR {
            static $gtype: GObject.GType<MemoryRequirements2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SparseImageMemoryRequirements2KHR {
            static $gtype: GObject.GType<SparseImageMemoryRequirements2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageFormatListCreateInfoKHR {
            static $gtype: GObject.GType<ImageFormatListCreateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SamplerYcbcrConversionKHR {
            static $gtype: GObject.GType<SamplerYcbcrConversionKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SamplerYcbcrModelConversionKHR {
            static $gtype: GObject.GType<SamplerYcbcrModelConversionKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SamplerYcbcrRangeKHR {
            static $gtype: GObject.GType<SamplerYcbcrRangeKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ChromaLocationKHR {
            static $gtype: GObject.GType<ChromaLocationKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SamplerYcbcrConversionCreateInfoKHR {
            static $gtype: GObject.GType<SamplerYcbcrConversionCreateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SamplerYcbcrConversionInfoKHR {
            static $gtype: GObject.GType<SamplerYcbcrConversionInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BindImagePlaneMemoryInfoKHR {
            static $gtype: GObject.GType<BindImagePlaneMemoryInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImagePlaneMemoryRequirementsInfoKHR {
            static $gtype: GObject.GType<ImagePlaneMemoryRequirementsInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceSamplerYcbcrConversionFeaturesKHR {
            static $gtype: GObject.GType<PhysicalDeviceSamplerYcbcrConversionFeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SamplerYcbcrConversionImageFormatPropertiesKHR {
            static $gtype: GObject.GType<SamplerYcbcrConversionImageFormatPropertiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BindBufferMemoryInfoKHR {
            static $gtype: GObject.GType<BindBufferMemoryInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BindImageMemoryInfoKHR {
            static $gtype: GObject.GType<BindImageMemoryInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceMaintenance3PropertiesKHR {
            static $gtype: GObject.GType<PhysicalDeviceMaintenance3PropertiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorSetLayoutSupportKHR {
            static $gtype: GObject.GType<DescriptorSetLayoutSupportKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShaderSubgroupExtendedTypesFeaturesKHR {
            static $gtype: GObject.GType<PhysicalDeviceShaderSubgroupExtendedTypesFeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDevice8BitStorageFeaturesKHR {
            static $gtype: GObject.GType<PhysicalDevice8BitStorageFeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShaderAtomicInt64FeaturesKHR {
            static $gtype: GObject.GType<PhysicalDeviceShaderAtomicInt64FeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShaderClockFeaturesKHR {
            static $gtype: GObject.GType<PhysicalDeviceShaderClockFeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoDecodeH265ProfileInfoKHR {
            static $gtype: GObject.GType<VideoDecodeH265ProfileInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoDecodeH265CapabilitiesKHR {
            static $gtype: GObject.GType<VideoDecodeH265CapabilitiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoDecodeH265SessionParametersAddInfoKHR {
            static $gtype: GObject.GType<VideoDecodeH265SessionParametersAddInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoDecodeH265SessionParametersCreateInfoKHR {
            static $gtype: GObject.GType<VideoDecodeH265SessionParametersCreateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoDecodeH265PictureInfoKHR {
            static $gtype: GObject.GType<VideoDecodeH265PictureInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoDecodeH265DpbSlotInfoKHR {
            static $gtype: GObject.GType<VideoDecodeH265DpbSlotInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class QueueGlobalPriorityKHR {
            static $gtype: GObject.GType<QueueGlobalPriorityKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceQueueGlobalPriorityCreateInfoKHR {
            static $gtype: GObject.GType<DeviceQueueGlobalPriorityCreateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceGlobalPriorityQueryFeaturesKHR {
            static $gtype: GObject.GType<PhysicalDeviceGlobalPriorityQueryFeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class QueueFamilyGlobalPriorityPropertiesKHR {
            static $gtype: GObject.GType<QueueFamilyGlobalPriorityPropertiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DriverIdKHR {
            static $gtype: GObject.GType<DriverIdKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ConformanceVersionKHR {
            static $gtype: GObject.GType<ConformanceVersionKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceDriverPropertiesKHR {
            static $gtype: GObject.GType<PhysicalDeviceDriverPropertiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ShaderFloatControlsIndependenceKHR {
            static $gtype: GObject.GType<ShaderFloatControlsIndependenceKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceFloatControlsPropertiesKHR {
            static $gtype: GObject.GType<PhysicalDeviceFloatControlsPropertiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ResolveModeFlagBitsKHR {
            static $gtype: GObject.GType<ResolveModeFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ResolveModeFlagsKHR {
            static $gtype: GObject.GType<ResolveModeFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SubpassDescriptionDepthStencilResolveKHR {
            static $gtype: GObject.GType<SubpassDescriptionDepthStencilResolveKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceDepthStencilResolvePropertiesKHR {
            static $gtype: GObject.GType<PhysicalDeviceDepthStencilResolvePropertiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SemaphoreTypeKHR {
            static $gtype: GObject.GType<SemaphoreTypeKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SemaphoreWaitFlagBitsKHR {
            static $gtype: GObject.GType<SemaphoreWaitFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SemaphoreWaitFlagsKHR {
            static $gtype: GObject.GType<SemaphoreWaitFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceTimelineSemaphoreFeaturesKHR {
            static $gtype: GObject.GType<PhysicalDeviceTimelineSemaphoreFeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceTimelineSemaphorePropertiesKHR {
            static $gtype: GObject.GType<PhysicalDeviceTimelineSemaphorePropertiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SemaphoreTypeCreateInfoKHR {
            static $gtype: GObject.GType<SemaphoreTypeCreateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class TimelineSemaphoreSubmitInfoKHR {
            static $gtype: GObject.GType<TimelineSemaphoreSubmitInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SemaphoreWaitInfoKHR {
            static $gtype: GObject.GType<SemaphoreWaitInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SemaphoreSignalInfoKHR {
            static $gtype: GObject.GType<SemaphoreSignalInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceVulkanMemoryModelFeaturesKHR {
            static $gtype: GObject.GType<PhysicalDeviceVulkanMemoryModelFeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShaderTerminateInvocationFeaturesKHR {
            static $gtype: GObject.GType<PhysicalDeviceShaderTerminateInvocationFeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class FragmentShadingRateCombinerOpKHR {
            static $gtype: GObject.GType<FragmentShadingRateCombinerOpKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class FragmentShadingRateAttachmentInfoKHR {
            static $gtype: GObject.GType<FragmentShadingRateAttachmentInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineFragmentShadingRateStateCreateInfoKHR {
            static $gtype: GObject.GType<PipelineFragmentShadingRateStateCreateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceFragmentShadingRateFeaturesKHR {
            static $gtype: GObject.GType<PhysicalDeviceFragmentShadingRateFeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceFragmentShadingRatePropertiesKHR {
            static $gtype: GObject.GType<PhysicalDeviceFragmentShadingRatePropertiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceFragmentShadingRateKHR {
            static $gtype: GObject.GType<PhysicalDeviceFragmentShadingRateKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceDynamicRenderingLocalReadFeaturesKHR {
            static $gtype: GObject.GType<PhysicalDeviceDynamicRenderingLocalReadFeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RenderingAttachmentLocationInfoKHR {
            static $gtype: GObject.GType<RenderingAttachmentLocationInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RenderingInputAttachmentIndexInfoKHR {
            static $gtype: GObject.GType<RenderingInputAttachmentIndexInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShaderQuadControlFeaturesKHR {
            static $gtype: GObject.GType<PhysicalDeviceShaderQuadControlFeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SurfaceProtectedCapabilitiesKHR {
            static $gtype: GObject.GType<SurfaceProtectedCapabilitiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceSeparateDepthStencilLayoutsFeaturesKHR {
            static $gtype: GObject.GType<PhysicalDeviceSeparateDepthStencilLayoutsFeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AttachmentReferenceStencilLayoutKHR {
            static $gtype: GObject.GType<AttachmentReferenceStencilLayoutKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AttachmentDescriptionStencilLayoutKHR {
            static $gtype: GObject.GType<AttachmentDescriptionStencilLayoutKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDevicePresentWaitFeaturesKHR {
            static $gtype: GObject.GType<PhysicalDevicePresentWaitFeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceUniformBufferStandardLayoutFeaturesKHR {
            static $gtype: GObject.GType<PhysicalDeviceUniformBufferStandardLayoutFeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceBufferDeviceAddressFeaturesKHR {
            static $gtype: GObject.GType<PhysicalDeviceBufferDeviceAddressFeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BufferDeviceAddressInfoKHR {
            static $gtype: GObject.GType<BufferDeviceAddressInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BufferOpaqueCaptureAddressCreateInfoKHR {
            static $gtype: GObject.GType<BufferOpaqueCaptureAddressCreateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MemoryOpaqueCaptureAddressAllocateInfoKHR {
            static $gtype: GObject.GType<MemoryOpaqueCaptureAddressAllocateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceMemoryOpaqueCaptureAddressInfoKHR {
            static $gtype: GObject.GType<DeviceMemoryOpaqueCaptureAddressInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeferredOperationKHR {
            static $gtype: GObject.GType<DeferredOperationKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineExecutableStatisticFormatKHR {
            static $gtype: GObject.GType<PipelineExecutableStatisticFormatKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDevicePipelineExecutablePropertiesFeaturesKHR {
            static $gtype: GObject.GType<PhysicalDevicePipelineExecutablePropertiesFeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineInfoKHR {
            static $gtype: GObject.GType<PipelineInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineExecutablePropertiesKHR {
            static $gtype: GObject.GType<PipelineExecutablePropertiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineExecutableInfoKHR {
            static $gtype: GObject.GType<PipelineExecutableInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineExecutableStatisticValueKHR {
            static $gtype: GObject.GType<PipelineExecutableStatisticValueKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineExecutableStatisticKHR {
            static $gtype: GObject.GType<PipelineExecutableStatisticKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineExecutableInternalRepresentationKHR {
            static $gtype: GObject.GType<PipelineExecutableInternalRepresentationKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MemoryUnmapFlagBitsKHR {
            static $gtype: GObject.GType<MemoryUnmapFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MemoryUnmapFlagsKHR {
            static $gtype: GObject.GType<MemoryUnmapFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MemoryMapInfoKHR {
            static $gtype: GObject.GType<MemoryMapInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MemoryUnmapInfoKHR {
            static $gtype: GObject.GType<MemoryUnmapInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShaderIntegerDotProductFeaturesKHR {
            static $gtype: GObject.GType<PhysicalDeviceShaderIntegerDotProductFeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShaderIntegerDotProductPropertiesKHR {
            static $gtype: GObject.GType<PhysicalDeviceShaderIntegerDotProductPropertiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineLibraryCreateInfoKHR {
            static $gtype: GObject.GType<PipelineLibraryCreateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PresentIdKHR {
            static $gtype: GObject.GType<PresentIdKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDevicePresentIdFeaturesKHR {
            static $gtype: GObject.GType<PhysicalDevicePresentIdFeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeTuningModeKHR {
            static $gtype: GObject.GType<VideoEncodeTuningModeKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeFlagBitsKHR {
            static $gtype: GObject.GType<VideoEncodeFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeFlagsKHR {
            static $gtype: GObject.GType<VideoEncodeFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeCapabilityFlagBitsKHR {
            static $gtype: GObject.GType<VideoEncodeCapabilityFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeCapabilityFlagsKHR {
            static $gtype: GObject.GType<VideoEncodeCapabilityFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeRateControlModeFlagBitsKHR {
            static $gtype: GObject.GType<VideoEncodeRateControlModeFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeRateControlModeFlagsKHR {
            static $gtype: GObject.GType<VideoEncodeRateControlModeFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeFeedbackFlagBitsKHR {
            static $gtype: GObject.GType<VideoEncodeFeedbackFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeFeedbackFlagsKHR {
            static $gtype: GObject.GType<VideoEncodeFeedbackFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeUsageFlagBitsKHR {
            static $gtype: GObject.GType<VideoEncodeUsageFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeUsageFlagsKHR {
            static $gtype: GObject.GType<VideoEncodeUsageFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeContentFlagBitsKHR {
            static $gtype: GObject.GType<VideoEncodeContentFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeContentFlagsKHR {
            static $gtype: GObject.GType<VideoEncodeContentFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeRateControlFlagsKHR {
            static $gtype: GObject.GType<VideoEncodeRateControlFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeInfoKHR {
            static $gtype: GObject.GType<VideoEncodeInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeCapabilitiesKHR {
            static $gtype: GObject.GType<VideoEncodeCapabilitiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class QueryPoolVideoEncodeFeedbackCreateInfoKHR {
            static $gtype: GObject.GType<QueryPoolVideoEncodeFeedbackCreateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeUsageInfoKHR {
            static $gtype: GObject.GType<VideoEncodeUsageInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeRateControlLayerInfoKHR {
            static $gtype: GObject.GType<VideoEncodeRateControlLayerInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeRateControlInfoKHR {
            static $gtype: GObject.GType<VideoEncodeRateControlInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceVideoEncodeQualityLevelInfoKHR {
            static $gtype: GObject.GType<PhysicalDeviceVideoEncodeQualityLevelInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeQualityLevelPropertiesKHR {
            static $gtype: GObject.GType<VideoEncodeQualityLevelPropertiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeQualityLevelInfoKHR {
            static $gtype: GObject.GType<VideoEncodeQualityLevelInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeSessionParametersGetInfoKHR {
            static $gtype: GObject.GType<VideoEncodeSessionParametersGetInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoEncodeSessionParametersFeedbackInfoKHR {
            static $gtype: GObject.GType<VideoEncodeSessionParametersFeedbackInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineStageFlags2KHR {
            static $gtype: GObject.GType<PipelineStageFlags2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineStageFlagBits2KHR {
            static $gtype: GObject.GType<PipelineStageFlagBits2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AccessFlags2KHR {
            static $gtype: GObject.GType<AccessFlags2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AccessFlagBits2KHR {
            static $gtype: GObject.GType<AccessFlagBits2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SubmitFlagBitsKHR {
            static $gtype: GObject.GType<SubmitFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SubmitFlagsKHR {
            static $gtype: GObject.GType<SubmitFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MemoryBarrier2KHR {
            static $gtype: GObject.GType<MemoryBarrier2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BufferMemoryBarrier2KHR {
            static $gtype: GObject.GType<BufferMemoryBarrier2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageMemoryBarrier2KHR {
            static $gtype: GObject.GType<ImageMemoryBarrier2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DependencyInfoKHR {
            static $gtype: GObject.GType<DependencyInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SubmitInfo2KHR {
            static $gtype: GObject.GType<SubmitInfo2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SemaphoreSubmitInfoKHR {
            static $gtype: GObject.GType<SemaphoreSubmitInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CommandBufferSubmitInfoKHR {
            static $gtype: GObject.GType<CommandBufferSubmitInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceSynchronization2FeaturesKHR {
            static $gtype: GObject.GType<PhysicalDeviceSynchronization2FeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class QueueFamilyCheckpointProperties2NV {
            static $gtype: GObject.GType<QueueFamilyCheckpointProperties2NV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CheckpointData2NV {
            static $gtype: GObject.GType<CheckpointData2NV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceFragmentShaderBarycentricFeaturesKHR {
            static $gtype: GObject.GType<PhysicalDeviceFragmentShaderBarycentricFeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceFragmentShaderBarycentricPropertiesKHR {
            static $gtype: GObject.GType<PhysicalDeviceFragmentShaderBarycentricPropertiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShaderSubgroupUniformControlFlowFeaturesKHR {
            static $gtype: GObject.GType<PhysicalDeviceShaderSubgroupUniformControlFlowFeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceZeroInitializeWorkgroupMemoryFeaturesKHR {
            static $gtype: GObject.GType<PhysicalDeviceZeroInitializeWorkgroupMemoryFeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceWorkgroupMemoryExplicitLayoutFeaturesKHR {
            static $gtype: GObject.GType<PhysicalDeviceWorkgroupMemoryExplicitLayoutFeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CopyBufferInfo2KHR {
            static $gtype: GObject.GType<CopyBufferInfo2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CopyImageInfo2KHR {
            static $gtype: GObject.GType<CopyImageInfo2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CopyBufferToImageInfo2KHR {
            static $gtype: GObject.GType<CopyBufferToImageInfo2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CopyImageToBufferInfo2KHR {
            static $gtype: GObject.GType<CopyImageToBufferInfo2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BlitImageInfo2KHR {
            static $gtype: GObject.GType<BlitImageInfo2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ResolveImageInfo2KHR {
            static $gtype: GObject.GType<ResolveImageInfo2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BufferCopy2KHR {
            static $gtype: GObject.GType<BufferCopy2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageCopy2KHR {
            static $gtype: GObject.GType<ImageCopy2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageBlit2KHR {
            static $gtype: GObject.GType<ImageBlit2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BufferImageCopy2KHR {
            static $gtype: GObject.GType<BufferImageCopy2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageResolve2KHR {
            static $gtype: GObject.GType<ImageResolve2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class FormatFeatureFlags2KHR {
            static $gtype: GObject.GType<FormatFeatureFlags2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class FormatFeatureFlagBits2KHR {
            static $gtype: GObject.GType<FormatFeatureFlagBits2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class FormatProperties3KHR {
            static $gtype: GObject.GType<FormatProperties3KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceRayTracingMaintenance1FeaturesKHR {
            static $gtype: GObject.GType<PhysicalDeviceRayTracingMaintenance1FeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class TraceRaysIndirectCommand2KHR {
            static $gtype: GObject.GType<TraceRaysIndirectCommand2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceMaintenance4FeaturesKHR {
            static $gtype: GObject.GType<PhysicalDeviceMaintenance4FeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceMaintenance4PropertiesKHR {
            static $gtype: GObject.GType<PhysicalDeviceMaintenance4PropertiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceBufferMemoryRequirementsKHR {
            static $gtype: GObject.GType<DeviceBufferMemoryRequirementsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceImageMemoryRequirementsKHR {
            static $gtype: GObject.GType<DeviceImageMemoryRequirementsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShaderSubgroupRotateFeaturesKHR {
            static $gtype: GObject.GType<PhysicalDeviceShaderSubgroupRotateFeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShaderMaximalReconvergenceFeaturesKHR {
            static $gtype: GObject.GType<PhysicalDeviceShaderMaximalReconvergenceFeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineCreateFlags2KHR {
            static $gtype: GObject.GType<PipelineCreateFlags2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineCreateFlagBits2KHR {
            static $gtype: GObject.GType<PipelineCreateFlagBits2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BufferUsageFlags2KHR {
            static $gtype: GObject.GType<BufferUsageFlags2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BufferUsageFlagBits2KHR {
            static $gtype: GObject.GType<BufferUsageFlagBits2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceMaintenance5FeaturesKHR {
            static $gtype: GObject.GType<PhysicalDeviceMaintenance5FeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceMaintenance5PropertiesKHR {
            static $gtype: GObject.GType<PhysicalDeviceMaintenance5PropertiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RenderingAreaInfoKHR {
            static $gtype: GObject.GType<RenderingAreaInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageSubresource2KHR {
            static $gtype: GObject.GType<ImageSubresource2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceImageSubresourceInfoKHR {
            static $gtype: GObject.GType<DeviceImageSubresourceInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SubresourceLayout2KHR {
            static $gtype: GObject.GType<SubresourceLayout2KHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineCreateFlags2CreateInfoKHR {
            static $gtype: GObject.GType<PipelineCreateFlags2CreateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BufferUsageFlags2CreateInfoKHR {
            static $gtype: GObject.GType<BufferUsageFlags2CreateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceRayTracingPositionFetchFeaturesKHR {
            static $gtype: GObject.GType<PhysicalDeviceRayTracingPositionFetchFeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ComponentTypeKHR {
            static $gtype: GObject.GType<ComponentTypeKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ScopeKHR {
            static $gtype: GObject.GType<ScopeKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CooperativeMatrixPropertiesKHR {
            static $gtype: GObject.GType<CooperativeMatrixPropertiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceCooperativeMatrixFeaturesKHR {
            static $gtype: GObject.GType<PhysicalDeviceCooperativeMatrixFeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceCooperativeMatrixPropertiesKHR {
            static $gtype: GObject.GType<PhysicalDeviceCooperativeMatrixPropertiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoDecodeAV1ProfileInfoKHR {
            static $gtype: GObject.GType<VideoDecodeAV1ProfileInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoDecodeAV1CapabilitiesKHR {
            static $gtype: GObject.GType<VideoDecodeAV1CapabilitiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoDecodeAV1SessionParametersCreateInfoKHR {
            static $gtype: GObject.GType<VideoDecodeAV1SessionParametersCreateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoDecodeAV1PictureInfoKHR {
            static $gtype: GObject.GType<VideoDecodeAV1PictureInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoDecodeAV1DpbSlotInfoKHR {
            static $gtype: GObject.GType<VideoDecodeAV1DpbSlotInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceVideoMaintenance1FeaturesKHR {
            static $gtype: GObject.GType<PhysicalDeviceVideoMaintenance1FeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VideoInlineQueryInfoKHR {
            static $gtype: GObject.GType<VideoInlineQueryInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceVertexAttributeDivisorPropertiesKHR {
            static $gtype: GObject.GType<PhysicalDeviceVertexAttributeDivisorPropertiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VertexInputBindingDivisorDescriptionKHR {
            static $gtype: GObject.GType<VertexInputBindingDivisorDescriptionKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineVertexInputDivisorStateCreateInfoKHR {
            static $gtype: GObject.GType<PipelineVertexInputDivisorStateCreateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceVertexAttributeDivisorFeaturesKHR {
            static $gtype: GObject.GType<PhysicalDeviceVertexAttributeDivisorFeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShaderFloatControls2FeaturesKHR {
            static $gtype: GObject.GType<PhysicalDeviceShaderFloatControls2FeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceIndexTypeUint8FeaturesKHR {
            static $gtype: GObject.GType<PhysicalDeviceIndexTypeUint8FeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class LineRasterizationModeKHR {
            static $gtype: GObject.GType<LineRasterizationModeKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceLineRasterizationFeaturesKHR {
            static $gtype: GObject.GType<PhysicalDeviceLineRasterizationFeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceLineRasterizationPropertiesKHR {
            static $gtype: GObject.GType<PhysicalDeviceLineRasterizationPropertiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineRasterizationLineStateCreateInfoKHR {
            static $gtype: GObject.GType<PipelineRasterizationLineStateCreateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class TimeDomainKHR {
            static $gtype: GObject.GType<TimeDomainKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CalibratedTimestampInfoKHR {
            static $gtype: GObject.GType<CalibratedTimestampInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShaderExpectAssumeFeaturesKHR {
            static $gtype: GObject.GType<PhysicalDeviceShaderExpectAssumeFeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceMaintenance6FeaturesKHR {
            static $gtype: GObject.GType<PhysicalDeviceMaintenance6FeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceMaintenance6PropertiesKHR {
            static $gtype: GObject.GType<PhysicalDeviceMaintenance6PropertiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BindMemoryStatusKHR {
            static $gtype: GObject.GType<BindMemoryStatusKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BindDescriptorSetsInfoKHR {
            static $gtype: GObject.GType<BindDescriptorSetsInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PushConstantsInfoKHR {
            static $gtype: GObject.GType<PushConstantsInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PushDescriptorSetInfoKHR {
            static $gtype: GObject.GType<PushDescriptorSetInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PushDescriptorSetWithTemplateInfoKHR {
            static $gtype: GObject.GType<PushDescriptorSetWithTemplateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SetDescriptorBufferOffsetsInfoEXT {
            static $gtype: GObject.GType<SetDescriptorBufferOffsetsInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BindDescriptorBufferEmbeddedSamplersInfoEXT {
            static $gtype: GObject.GType<BindDescriptorBufferEmbeddedSamplersInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DebugReportCallbackEXT {
            static $gtype: GObject.GType<DebugReportCallbackEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DebugReportObjectTypeEXT {
            static $gtype: GObject.GType<DebugReportObjectTypeEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DebugReportFlagBitsEXT {
            static $gtype: GObject.GType<DebugReportFlagBitsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DebugReportFlagsEXT {
            static $gtype: GObject.GType<DebugReportFlagsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DebugReportCallbackCreateInfoEXT {
            static $gtype: GObject.GType<DebugReportCallbackCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RasterizationOrderAMD {
            static $gtype: GObject.GType<RasterizationOrderAMD>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineRasterizationStateRasterizationOrderAMD {
            static $gtype: GObject.GType<PipelineRasterizationStateRasterizationOrderAMD>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DebugMarkerObjectNameInfoEXT {
            static $gtype: GObject.GType<DebugMarkerObjectNameInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DebugMarkerObjectTagInfoEXT {
            static $gtype: GObject.GType<DebugMarkerObjectTagInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DebugMarkerMarkerInfoEXT {
            static $gtype: GObject.GType<DebugMarkerMarkerInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DedicatedAllocationImageCreateInfoNV {
            static $gtype: GObject.GType<DedicatedAllocationImageCreateInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DedicatedAllocationBufferCreateInfoNV {
            static $gtype: GObject.GType<DedicatedAllocationBufferCreateInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DedicatedAllocationMemoryAllocateInfoNV {
            static $gtype: GObject.GType<DedicatedAllocationMemoryAllocateInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineRasterizationStateStreamCreateFlagsEXT {
            static $gtype: GObject.GType<PipelineRasterizationStateStreamCreateFlagsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceTransformFeedbackFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceTransformFeedbackFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceTransformFeedbackPropertiesEXT {
            static $gtype: GObject.GType<PhysicalDeviceTransformFeedbackPropertiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineRasterizationStateStreamCreateInfoEXT {
            static $gtype: GObject.GType<PipelineRasterizationStateStreamCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CuModuleNVX {
            static $gtype: GObject.GType<CuModuleNVX>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CuFunctionNVX {
            static $gtype: GObject.GType<CuFunctionNVX>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CuModuleCreateInfoNVX {
            static $gtype: GObject.GType<CuModuleCreateInfoNVX>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CuFunctionCreateInfoNVX {
            static $gtype: GObject.GType<CuFunctionCreateInfoNVX>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CuLaunchInfoNVX {
            static $gtype: GObject.GType<CuLaunchInfoNVX>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageViewHandleInfoNVX {
            static $gtype: GObject.GType<ImageViewHandleInfoNVX>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageViewAddressPropertiesNVX {
            static $gtype: GObject.GType<ImageViewAddressPropertiesNVX>;

            // Constructors

            _init(...args: any[]): void;
        }

        class TextureLODGatherFormatPropertiesAMD {
            static $gtype: GObject.GType<TextureLODGatherFormatPropertiesAMD>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ShaderInfoTypeAMD {
            static $gtype: GObject.GType<ShaderInfoTypeAMD>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ShaderResourceUsageAMD {
            static $gtype: GObject.GType<ShaderResourceUsageAMD>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ShaderStatisticsInfoAMD {
            static $gtype: GObject.GType<ShaderStatisticsInfoAMD>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceCornerSampledImageFeaturesNV {
            static $gtype: GObject.GType<PhysicalDeviceCornerSampledImageFeaturesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalMemoryHandleTypeFlagBitsNV {
            static $gtype: GObject.GType<ExternalMemoryHandleTypeFlagBitsNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalMemoryHandleTypeFlagsNV {
            static $gtype: GObject.GType<ExternalMemoryHandleTypeFlagsNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalMemoryFeatureFlagBitsNV {
            static $gtype: GObject.GType<ExternalMemoryFeatureFlagBitsNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalMemoryFeatureFlagsNV {
            static $gtype: GObject.GType<ExternalMemoryFeatureFlagsNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalImageFormatPropertiesNV {
            static $gtype: GObject.GType<ExternalImageFormatPropertiesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalMemoryImageCreateInfoNV {
            static $gtype: GObject.GType<ExternalMemoryImageCreateInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExportMemoryAllocateInfoNV {
            static $gtype: GObject.GType<ExportMemoryAllocateInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ValidationCheckEXT {
            static $gtype: GObject.GType<ValidationCheckEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ValidationFlagsEXT {
            static $gtype: GObject.GType<ValidationFlagsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceTextureCompressionASTCHDRFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceTextureCompressionASTCHDRFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageViewASTCDecodeModeEXT {
            static $gtype: GObject.GType<ImageViewASTCDecodeModeEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceASTCDecodeFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceASTCDecodeFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineRobustnessBufferBehaviorEXT {
            static $gtype: GObject.GType<PipelineRobustnessBufferBehaviorEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineRobustnessImageBehaviorEXT {
            static $gtype: GObject.GType<PipelineRobustnessImageBehaviorEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDevicePipelineRobustnessFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDevicePipelineRobustnessFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDevicePipelineRobustnessPropertiesEXT {
            static $gtype: GObject.GType<PhysicalDevicePipelineRobustnessPropertiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineRobustnessCreateInfoEXT {
            static $gtype: GObject.GType<PipelineRobustnessCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ConditionalRenderingFlagBitsEXT {
            static $gtype: GObject.GType<ConditionalRenderingFlagBitsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ConditionalRenderingFlagsEXT {
            static $gtype: GObject.GType<ConditionalRenderingFlagsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ConditionalRenderingBeginInfoEXT {
            static $gtype: GObject.GType<ConditionalRenderingBeginInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceConditionalRenderingFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceConditionalRenderingFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CommandBufferInheritanceConditionalRenderingInfoEXT {
            static $gtype: GObject.GType<CommandBufferInheritanceConditionalRenderingInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ViewportWScalingNV {
            static $gtype: GObject.GType<ViewportWScalingNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineViewportWScalingStateCreateInfoNV {
            static $gtype: GObject.GType<PipelineViewportWScalingStateCreateInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SurfaceCounterFlagBitsEXT {
            static $gtype: GObject.GType<SurfaceCounterFlagBitsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SurfaceCounterFlagsEXT {
            static $gtype: GObject.GType<SurfaceCounterFlagsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SurfaceCapabilities2EXT {
            static $gtype: GObject.GType<SurfaceCapabilities2EXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DisplayPowerStateEXT {
            static $gtype: GObject.GType<DisplayPowerStateEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceEventTypeEXT {
            static $gtype: GObject.GType<DeviceEventTypeEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DisplayEventTypeEXT {
            static $gtype: GObject.GType<DisplayEventTypeEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DisplayPowerInfoEXT {
            static $gtype: GObject.GType<DisplayPowerInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceEventInfoEXT {
            static $gtype: GObject.GType<DeviceEventInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DisplayEventInfoEXT {
            static $gtype: GObject.GType<DisplayEventInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SwapchainCounterCreateInfoEXT {
            static $gtype: GObject.GType<SwapchainCounterCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RefreshCycleDurationGOOGLE {
            static $gtype: GObject.GType<RefreshCycleDurationGOOGLE>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PastPresentationTimingGOOGLE {
            static $gtype: GObject.GType<PastPresentationTimingGOOGLE>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PresentTimeGOOGLE {
            static $gtype: GObject.GType<PresentTimeGOOGLE>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PresentTimesInfoGOOGLE {
            static $gtype: GObject.GType<PresentTimesInfoGOOGLE>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceMultiviewPerViewAttributesPropertiesNVX {
            static $gtype: GObject.GType<PhysicalDeviceMultiviewPerViewAttributesPropertiesNVX>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ViewportCoordinateSwizzleNV {
            static $gtype: GObject.GType<ViewportCoordinateSwizzleNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineViewportSwizzleStateCreateFlagsNV {
            static $gtype: GObject.GType<PipelineViewportSwizzleStateCreateFlagsNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ViewportSwizzleNV {
            static $gtype: GObject.GType<ViewportSwizzleNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineViewportSwizzleStateCreateInfoNV {
            static $gtype: GObject.GType<PipelineViewportSwizzleStateCreateInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DiscardRectangleModeEXT {
            static $gtype: GObject.GType<DiscardRectangleModeEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineDiscardRectangleStateCreateFlagsEXT {
            static $gtype: GObject.GType<PipelineDiscardRectangleStateCreateFlagsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceDiscardRectanglePropertiesEXT {
            static $gtype: GObject.GType<PhysicalDeviceDiscardRectanglePropertiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineDiscardRectangleStateCreateInfoEXT {
            static $gtype: GObject.GType<PipelineDiscardRectangleStateCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ConservativeRasterizationModeEXT {
            static $gtype: GObject.GType<ConservativeRasterizationModeEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineRasterizationConservativeStateCreateFlagsEXT {
            static $gtype: GObject.GType<PipelineRasterizationConservativeStateCreateFlagsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceConservativeRasterizationPropertiesEXT {
            static $gtype: GObject.GType<PhysicalDeviceConservativeRasterizationPropertiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineRasterizationConservativeStateCreateInfoEXT {
            static $gtype: GObject.GType<PipelineRasterizationConservativeStateCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineRasterizationDepthClipStateCreateFlagsEXT {
            static $gtype: GObject.GType<PipelineRasterizationDepthClipStateCreateFlagsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceDepthClipEnableFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceDepthClipEnableFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineRasterizationDepthClipStateCreateInfoEXT {
            static $gtype: GObject.GType<PipelineRasterizationDepthClipStateCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class XYColorEXT {
            static $gtype: GObject.GType<XYColorEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class HdrMetadataEXT {
            static $gtype: GObject.GType<HdrMetadataEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceRelaxedLineRasterizationFeaturesIMG {
            static $gtype: GObject.GType<PhysicalDeviceRelaxedLineRasterizationFeaturesIMG>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DebugUtilsMessengerEXT {
            static $gtype: GObject.GType<DebugUtilsMessengerEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DebugUtilsMessengerCallbackDataFlagsEXT {
            static $gtype: GObject.GType<DebugUtilsMessengerCallbackDataFlagsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DebugUtilsMessageSeverityFlagBitsEXT {
            static $gtype: GObject.GType<DebugUtilsMessageSeverityFlagBitsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DebugUtilsMessageTypeFlagBitsEXT {
            static $gtype: GObject.GType<DebugUtilsMessageTypeFlagBitsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DebugUtilsMessageTypeFlagsEXT {
            static $gtype: GObject.GType<DebugUtilsMessageTypeFlagsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DebugUtilsMessageSeverityFlagsEXT {
            static $gtype: GObject.GType<DebugUtilsMessageSeverityFlagsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DebugUtilsMessengerCreateFlagsEXT {
            static $gtype: GObject.GType<DebugUtilsMessengerCreateFlagsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DebugUtilsLabelEXT {
            static $gtype: GObject.GType<DebugUtilsLabelEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DebugUtilsObjectNameInfoEXT {
            static $gtype: GObject.GType<DebugUtilsObjectNameInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DebugUtilsMessengerCallbackDataEXT {
            static $gtype: GObject.GType<DebugUtilsMessengerCallbackDataEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DebugUtilsMessengerCreateInfoEXT {
            static $gtype: GObject.GType<DebugUtilsMessengerCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DebugUtilsObjectTagInfoEXT {
            static $gtype: GObject.GType<DebugUtilsObjectTagInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SamplerReductionModeEXT {
            static $gtype: GObject.GType<SamplerReductionModeEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SamplerReductionModeCreateInfoEXT {
            static $gtype: GObject.GType<SamplerReductionModeCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceSamplerFilterMinmaxPropertiesEXT {
            static $gtype: GObject.GType<PhysicalDeviceSamplerFilterMinmaxPropertiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceInlineUniformBlockFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceInlineUniformBlockFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceInlineUniformBlockPropertiesEXT {
            static $gtype: GObject.GType<PhysicalDeviceInlineUniformBlockPropertiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class WriteDescriptorSetInlineUniformBlockEXT {
            static $gtype: GObject.GType<WriteDescriptorSetInlineUniformBlockEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorPoolInlineUniformBlockCreateInfoEXT {
            static $gtype: GObject.GType<DescriptorPoolInlineUniformBlockCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SampleLocationEXT {
            static $gtype: GObject.GType<SampleLocationEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SampleLocationsInfoEXT {
            static $gtype: GObject.GType<SampleLocationsInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AttachmentSampleLocationsEXT {
            static $gtype: GObject.GType<AttachmentSampleLocationsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SubpassSampleLocationsEXT {
            static $gtype: GObject.GType<SubpassSampleLocationsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RenderPassSampleLocationsBeginInfoEXT {
            static $gtype: GObject.GType<RenderPassSampleLocationsBeginInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineSampleLocationsStateCreateInfoEXT {
            static $gtype: GObject.GType<PipelineSampleLocationsStateCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceSampleLocationsPropertiesEXT {
            static $gtype: GObject.GType<PhysicalDeviceSampleLocationsPropertiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MultisamplePropertiesEXT {
            static $gtype: GObject.GType<MultisamplePropertiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BlendOverlapEXT {
            static $gtype: GObject.GType<BlendOverlapEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceBlendOperationAdvancedFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceBlendOperationAdvancedFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceBlendOperationAdvancedPropertiesEXT {
            static $gtype: GObject.GType<PhysicalDeviceBlendOperationAdvancedPropertiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineColorBlendAdvancedStateCreateInfoEXT {
            static $gtype: GObject.GType<PipelineColorBlendAdvancedStateCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineCoverageToColorStateCreateFlagsNV {
            static $gtype: GObject.GType<PipelineCoverageToColorStateCreateFlagsNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineCoverageToColorStateCreateInfoNV {
            static $gtype: GObject.GType<PipelineCoverageToColorStateCreateInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CoverageModulationModeNV {
            static $gtype: GObject.GType<CoverageModulationModeNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineCoverageModulationStateCreateFlagsNV {
            static $gtype: GObject.GType<PipelineCoverageModulationStateCreateFlagsNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineCoverageModulationStateCreateInfoNV {
            static $gtype: GObject.GType<PipelineCoverageModulationStateCreateInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShaderSMBuiltinsPropertiesNV {
            static $gtype: GObject.GType<PhysicalDeviceShaderSMBuiltinsPropertiesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShaderSMBuiltinsFeaturesNV {
            static $gtype: GObject.GType<PhysicalDeviceShaderSMBuiltinsFeaturesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DrmFormatModifierPropertiesEXT {
            static $gtype: GObject.GType<DrmFormatModifierPropertiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DrmFormatModifierPropertiesListEXT {
            static $gtype: GObject.GType<DrmFormatModifierPropertiesListEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceImageDrmFormatModifierInfoEXT {
            static $gtype: GObject.GType<PhysicalDeviceImageDrmFormatModifierInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageDrmFormatModifierListCreateInfoEXT {
            static $gtype: GObject.GType<ImageDrmFormatModifierListCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageDrmFormatModifierExplicitCreateInfoEXT {
            static $gtype: GObject.GType<ImageDrmFormatModifierExplicitCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageDrmFormatModifierPropertiesEXT {
            static $gtype: GObject.GType<ImageDrmFormatModifierPropertiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DrmFormatModifierProperties2EXT {
            static $gtype: GObject.GType<DrmFormatModifierProperties2EXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DrmFormatModifierPropertiesList2EXT {
            static $gtype: GObject.GType<DrmFormatModifierPropertiesList2EXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ValidationCacheEXT {
            static $gtype: GObject.GType<ValidationCacheEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ValidationCacheHeaderVersionEXT {
            static $gtype: GObject.GType<ValidationCacheHeaderVersionEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ValidationCacheCreateFlagsEXT {
            static $gtype: GObject.GType<ValidationCacheCreateFlagsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ValidationCacheCreateInfoEXT {
            static $gtype: GObject.GType<ValidationCacheCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ShaderModuleValidationCacheCreateInfoEXT {
            static $gtype: GObject.GType<ShaderModuleValidationCacheCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorBindingFlagBitsEXT {
            static $gtype: GObject.GType<DescriptorBindingFlagBitsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorBindingFlagsEXT {
            static $gtype: GObject.GType<DescriptorBindingFlagsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorSetLayoutBindingFlagsCreateInfoEXT {
            static $gtype: GObject.GType<DescriptorSetLayoutBindingFlagsCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceDescriptorIndexingFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceDescriptorIndexingFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceDescriptorIndexingPropertiesEXT {
            static $gtype: GObject.GType<PhysicalDeviceDescriptorIndexingPropertiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorSetVariableDescriptorCountAllocateInfoEXT {
            static $gtype: GObject.GType<DescriptorSetVariableDescriptorCountAllocateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorSetVariableDescriptorCountLayoutSupportEXT {
            static $gtype: GObject.GType<DescriptorSetVariableDescriptorCountLayoutSupportEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ShadingRatePaletteEntryNV {
            static $gtype: GObject.GType<ShadingRatePaletteEntryNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CoarseSampleOrderTypeNV {
            static $gtype: GObject.GType<CoarseSampleOrderTypeNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ShadingRatePaletteNV {
            static $gtype: GObject.GType<ShadingRatePaletteNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineViewportShadingRateImageStateCreateInfoNV {
            static $gtype: GObject.GType<PipelineViewportShadingRateImageStateCreateInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShadingRateImageFeaturesNV {
            static $gtype: GObject.GType<PhysicalDeviceShadingRateImageFeaturesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShadingRateImagePropertiesNV {
            static $gtype: GObject.GType<PhysicalDeviceShadingRateImagePropertiesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CoarseSampleLocationNV {
            static $gtype: GObject.GType<CoarseSampleLocationNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CoarseSampleOrderCustomNV {
            static $gtype: GObject.GType<CoarseSampleOrderCustomNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineViewportCoarseSampleOrderStateCreateInfoNV {
            static $gtype: GObject.GType<PipelineViewportCoarseSampleOrderStateCreateInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AccelerationStructureNV {
            static $gtype: GObject.GType<AccelerationStructureNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RayTracingShaderGroupTypeKHR {
            static $gtype: GObject.GType<RayTracingShaderGroupTypeKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RayTracingShaderGroupTypeNV {
            static $gtype: GObject.GType<RayTracingShaderGroupTypeNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class GeometryTypeKHR {
            static $gtype: GObject.GType<GeometryTypeKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class GeometryTypeNV {
            static $gtype: GObject.GType<GeometryTypeNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AccelerationStructureTypeKHR {
            static $gtype: GObject.GType<AccelerationStructureTypeKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AccelerationStructureTypeNV {
            static $gtype: GObject.GType<AccelerationStructureTypeNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CopyAccelerationStructureModeKHR {
            static $gtype: GObject.GType<CopyAccelerationStructureModeKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CopyAccelerationStructureModeNV {
            static $gtype: GObject.GType<CopyAccelerationStructureModeNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AccelerationStructureMemoryRequirementsTypeNV {
            static $gtype: GObject.GType<AccelerationStructureMemoryRequirementsTypeNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class GeometryFlagBitsKHR {
            static $gtype: GObject.GType<GeometryFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class GeometryFlagsKHR {
            static $gtype: GObject.GType<GeometryFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class GeometryFlagsNV {
            static $gtype: GObject.GType<GeometryFlagsNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class GeometryFlagBitsNV {
            static $gtype: GObject.GType<GeometryFlagBitsNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class GeometryInstanceFlagBitsKHR {
            static $gtype: GObject.GType<GeometryInstanceFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class GeometryInstanceFlagsKHR {
            static $gtype: GObject.GType<GeometryInstanceFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class GeometryInstanceFlagsNV {
            static $gtype: GObject.GType<GeometryInstanceFlagsNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class GeometryInstanceFlagBitsNV {
            static $gtype: GObject.GType<GeometryInstanceFlagBitsNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BuildAccelerationStructureFlagBitsKHR {
            static $gtype: GObject.GType<BuildAccelerationStructureFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BuildAccelerationStructureFlagsKHR {
            static $gtype: GObject.GType<BuildAccelerationStructureFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BuildAccelerationStructureFlagsNV {
            static $gtype: GObject.GType<BuildAccelerationStructureFlagsNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BuildAccelerationStructureFlagBitsNV {
            static $gtype: GObject.GType<BuildAccelerationStructureFlagBitsNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RayTracingShaderGroupCreateInfoNV {
            static $gtype: GObject.GType<RayTracingShaderGroupCreateInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RayTracingPipelineCreateInfoNV {
            static $gtype: GObject.GType<RayTracingPipelineCreateInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class GeometryTrianglesNV {
            static $gtype: GObject.GType<GeometryTrianglesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class GeometryAABBNV {
            static $gtype: GObject.GType<GeometryAABBNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class GeometryDataNV {
            static $gtype: GObject.GType<GeometryDataNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class GeometryNV {
            static $gtype: GObject.GType<GeometryNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AccelerationStructureInfoNV {
            static $gtype: GObject.GType<AccelerationStructureInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AccelerationStructureCreateInfoNV {
            static $gtype: GObject.GType<AccelerationStructureCreateInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BindAccelerationStructureMemoryInfoNV {
            static $gtype: GObject.GType<BindAccelerationStructureMemoryInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class WriteDescriptorSetAccelerationStructureNV {
            static $gtype: GObject.GType<WriteDescriptorSetAccelerationStructureNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AccelerationStructureMemoryRequirementsInfoNV {
            static $gtype: GObject.GType<AccelerationStructureMemoryRequirementsInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceRayTracingPropertiesNV {
            static $gtype: GObject.GType<PhysicalDeviceRayTracingPropertiesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class TransformMatrixKHR {
            static $gtype: GObject.GType<TransformMatrixKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class TransformMatrixNV {
            static $gtype: GObject.GType<TransformMatrixNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AabbPositionsKHR {
            static $gtype: GObject.GType<AabbPositionsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AabbPositionsNV {
            static $gtype: GObject.GType<AabbPositionsNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AccelerationStructureInstanceKHR {
            static $gtype: GObject.GType<AccelerationStructureInstanceKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AccelerationStructureInstanceNV {
            static $gtype: GObject.GType<AccelerationStructureInstanceNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceRepresentativeFragmentTestFeaturesNV {
            static $gtype: GObject.GType<PhysicalDeviceRepresentativeFragmentTestFeaturesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineRepresentativeFragmentTestStateCreateInfoNV {
            static $gtype: GObject.GType<PipelineRepresentativeFragmentTestStateCreateInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceImageViewImageFormatInfoEXT {
            static $gtype: GObject.GType<PhysicalDeviceImageViewImageFormatInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class FilterCubicImageViewImageFormatPropertiesEXT {
            static $gtype: GObject.GType<FilterCubicImageViewImageFormatPropertiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class QueueGlobalPriorityEXT {
            static $gtype: GObject.GType<QueueGlobalPriorityEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceQueueGlobalPriorityCreateInfoEXT {
            static $gtype: GObject.GType<DeviceQueueGlobalPriorityCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImportMemoryHostPointerInfoEXT {
            static $gtype: GObject.GType<ImportMemoryHostPointerInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MemoryHostPointerPropertiesEXT {
            static $gtype: GObject.GType<MemoryHostPointerPropertiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceExternalMemoryHostPropertiesEXT {
            static $gtype: GObject.GType<PhysicalDeviceExternalMemoryHostPropertiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineCompilerControlFlagBitsAMD {
            static $gtype: GObject.GType<PipelineCompilerControlFlagBitsAMD>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineCompilerControlFlagsAMD {
            static $gtype: GObject.GType<PipelineCompilerControlFlagsAMD>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineCompilerControlCreateInfoAMD {
            static $gtype: GObject.GType<PipelineCompilerControlCreateInfoAMD>;

            // Constructors

            _init(...args: any[]): void;
        }

        class TimeDomainEXT {
            static $gtype: GObject.GType<TimeDomainEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CalibratedTimestampInfoEXT {
            static $gtype: GObject.GType<CalibratedTimestampInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShaderCorePropertiesAMD {
            static $gtype: GObject.GType<PhysicalDeviceShaderCorePropertiesAMD>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MemoryOverallocationBehaviorAMD {
            static $gtype: GObject.GType<MemoryOverallocationBehaviorAMD>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceMemoryOverallocationCreateInfoAMD {
            static $gtype: GObject.GType<DeviceMemoryOverallocationCreateInfoAMD>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceVertexAttributeDivisorPropertiesEXT {
            static $gtype: GObject.GType<PhysicalDeviceVertexAttributeDivisorPropertiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VertexInputBindingDivisorDescriptionEXT {
            static $gtype: GObject.GType<VertexInputBindingDivisorDescriptionEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineVertexInputDivisorStateCreateInfoEXT {
            static $gtype: GObject.GType<PipelineVertexInputDivisorStateCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceVertexAttributeDivisorFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceVertexAttributeDivisorFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineCreationFeedbackFlagBitsEXT {
            static $gtype: GObject.GType<PipelineCreationFeedbackFlagBitsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineCreationFeedbackFlagsEXT {
            static $gtype: GObject.GType<PipelineCreationFeedbackFlagsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineCreationFeedbackCreateInfoEXT {
            static $gtype: GObject.GType<PipelineCreationFeedbackCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineCreationFeedbackEXT {
            static $gtype: GObject.GType<PipelineCreationFeedbackEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceComputeShaderDerivativesFeaturesNV {
            static $gtype: GObject.GType<PhysicalDeviceComputeShaderDerivativesFeaturesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceMeshShaderFeaturesNV {
            static $gtype: GObject.GType<PhysicalDeviceMeshShaderFeaturesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceMeshShaderPropertiesNV {
            static $gtype: GObject.GType<PhysicalDeviceMeshShaderPropertiesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DrawMeshTasksIndirectCommandNV {
            static $gtype: GObject.GType<DrawMeshTasksIndirectCommandNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceFragmentShaderBarycentricFeaturesNV {
            static $gtype: GObject.GType<PhysicalDeviceFragmentShaderBarycentricFeaturesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShaderImageFootprintFeaturesNV {
            static $gtype: GObject.GType<PhysicalDeviceShaderImageFootprintFeaturesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineViewportExclusiveScissorStateCreateInfoNV {
            static $gtype: GObject.GType<PipelineViewportExclusiveScissorStateCreateInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceExclusiveScissorFeaturesNV {
            static $gtype: GObject.GType<PhysicalDeviceExclusiveScissorFeaturesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class QueueFamilyCheckpointPropertiesNV {
            static $gtype: GObject.GType<QueueFamilyCheckpointPropertiesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CheckpointDataNV {
            static $gtype: GObject.GType<CheckpointDataNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShaderIntegerFunctions2FeaturesINTEL {
            static $gtype: GObject.GType<PhysicalDeviceShaderIntegerFunctions2FeaturesINTEL>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PerformanceConfigurationINTEL {
            static $gtype: GObject.GType<PerformanceConfigurationINTEL>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PerformanceConfigurationTypeINTEL {
            static $gtype: GObject.GType<PerformanceConfigurationTypeINTEL>;

            // Constructors

            _init(...args: any[]): void;
        }

        class QueryPoolSamplingModeINTEL {
            static $gtype: GObject.GType<QueryPoolSamplingModeINTEL>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PerformanceOverrideTypeINTEL {
            static $gtype: GObject.GType<PerformanceOverrideTypeINTEL>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PerformanceParameterTypeINTEL {
            static $gtype: GObject.GType<PerformanceParameterTypeINTEL>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PerformanceValueTypeINTEL {
            static $gtype: GObject.GType<PerformanceValueTypeINTEL>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PerformanceValueDataINTEL {
            static $gtype: GObject.GType<PerformanceValueDataINTEL>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PerformanceValueINTEL {
            static $gtype: GObject.GType<PerformanceValueINTEL>;

            // Constructors

            _init(...args: any[]): void;
        }

        class InitializePerformanceApiInfoINTEL {
            static $gtype: GObject.GType<InitializePerformanceApiInfoINTEL>;

            // Constructors

            _init(...args: any[]): void;
        }

        class QueryPoolPerformanceQueryCreateInfoINTEL {
            static $gtype: GObject.GType<QueryPoolPerformanceQueryCreateInfoINTEL>;

            // Constructors

            _init(...args: any[]): void;
        }

        class QueryPoolCreateInfoINTEL {
            static $gtype: GObject.GType<QueryPoolCreateInfoINTEL>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PerformanceMarkerInfoINTEL {
            static $gtype: GObject.GType<PerformanceMarkerInfoINTEL>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PerformanceStreamMarkerInfoINTEL {
            static $gtype: GObject.GType<PerformanceStreamMarkerInfoINTEL>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PerformanceOverrideInfoINTEL {
            static $gtype: GObject.GType<PerformanceOverrideInfoINTEL>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PerformanceConfigurationAcquireInfoINTEL {
            static $gtype: GObject.GType<PerformanceConfigurationAcquireInfoINTEL>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDevicePCIBusInfoPropertiesEXT {
            static $gtype: GObject.GType<PhysicalDevicePCIBusInfoPropertiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DisplayNativeHdrSurfaceCapabilitiesAMD {
            static $gtype: GObject.GType<DisplayNativeHdrSurfaceCapabilitiesAMD>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SwapchainDisplayNativeHdrCreateInfoAMD {
            static $gtype: GObject.GType<SwapchainDisplayNativeHdrCreateInfoAMD>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceFragmentDensityMapFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceFragmentDensityMapFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceFragmentDensityMapPropertiesEXT {
            static $gtype: GObject.GType<PhysicalDeviceFragmentDensityMapPropertiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RenderPassFragmentDensityMapCreateInfoEXT {
            static $gtype: GObject.GType<RenderPassFragmentDensityMapCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceScalarBlockLayoutFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceScalarBlockLayoutFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceSubgroupSizeControlFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceSubgroupSizeControlFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceSubgroupSizeControlPropertiesEXT {
            static $gtype: GObject.GType<PhysicalDeviceSubgroupSizeControlPropertiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineShaderStageRequiredSubgroupSizeCreateInfoEXT {
            static $gtype: GObject.GType<PipelineShaderStageRequiredSubgroupSizeCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ShaderCorePropertiesFlagBitsAMD {
            static $gtype: GObject.GType<ShaderCorePropertiesFlagBitsAMD>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ShaderCorePropertiesFlagsAMD {
            static $gtype: GObject.GType<ShaderCorePropertiesFlagsAMD>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShaderCoreProperties2AMD {
            static $gtype: GObject.GType<PhysicalDeviceShaderCoreProperties2AMD>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceCoherentMemoryFeaturesAMD {
            static $gtype: GObject.GType<PhysicalDeviceCoherentMemoryFeaturesAMD>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShaderImageAtomicInt64FeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceShaderImageAtomicInt64FeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceMemoryBudgetPropertiesEXT {
            static $gtype: GObject.GType<PhysicalDeviceMemoryBudgetPropertiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceMemoryPriorityFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceMemoryPriorityFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MemoryPriorityAllocateInfoEXT {
            static $gtype: GObject.GType<MemoryPriorityAllocateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceDedicatedAllocationImageAliasingFeaturesNV {
            static $gtype: GObject.GType<PhysicalDeviceDedicatedAllocationImageAliasingFeaturesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceBufferDeviceAddressFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceBufferDeviceAddressFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceBufferAddressFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceBufferAddressFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BufferDeviceAddressInfoEXT {
            static $gtype: GObject.GType<BufferDeviceAddressInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BufferDeviceAddressCreateInfoEXT {
            static $gtype: GObject.GType<BufferDeviceAddressCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ToolPurposeFlagBitsEXT {
            static $gtype: GObject.GType<ToolPurposeFlagBitsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ToolPurposeFlagsEXT {
            static $gtype: GObject.GType<ToolPurposeFlagsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceToolPropertiesEXT {
            static $gtype: GObject.GType<PhysicalDeviceToolPropertiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageStencilUsageCreateInfoEXT {
            static $gtype: GObject.GType<ImageStencilUsageCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ValidationFeatureEnableEXT {
            static $gtype: GObject.GType<ValidationFeatureEnableEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ValidationFeatureDisableEXT {
            static $gtype: GObject.GType<ValidationFeatureDisableEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ValidationFeaturesEXT {
            static $gtype: GObject.GType<ValidationFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ComponentTypeNV {
            static $gtype: GObject.GType<ComponentTypeNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ScopeNV {
            static $gtype: GObject.GType<ScopeNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CooperativeMatrixPropertiesNV {
            static $gtype: GObject.GType<CooperativeMatrixPropertiesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceCooperativeMatrixFeaturesNV {
            static $gtype: GObject.GType<PhysicalDeviceCooperativeMatrixFeaturesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceCooperativeMatrixPropertiesNV {
            static $gtype: GObject.GType<PhysicalDeviceCooperativeMatrixPropertiesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CoverageReductionModeNV {
            static $gtype: GObject.GType<CoverageReductionModeNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineCoverageReductionStateCreateFlagsNV {
            static $gtype: GObject.GType<PipelineCoverageReductionStateCreateFlagsNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceCoverageReductionModeFeaturesNV {
            static $gtype: GObject.GType<PhysicalDeviceCoverageReductionModeFeaturesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineCoverageReductionStateCreateInfoNV {
            static $gtype: GObject.GType<PipelineCoverageReductionStateCreateInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class FramebufferMixedSamplesCombinationNV {
            static $gtype: GObject.GType<FramebufferMixedSamplesCombinationNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceFragmentShaderInterlockFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceFragmentShaderInterlockFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceYcbcrImageArraysFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceYcbcrImageArraysFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ProvokingVertexModeEXT {
            static $gtype: GObject.GType<ProvokingVertexModeEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceProvokingVertexFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceProvokingVertexFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceProvokingVertexPropertiesEXT {
            static $gtype: GObject.GType<PhysicalDeviceProvokingVertexPropertiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineRasterizationProvokingVertexStateCreateInfoEXT {
            static $gtype: GObject.GType<PipelineRasterizationProvokingVertexStateCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class HeadlessSurfaceCreateFlagsEXT {
            static $gtype: GObject.GType<HeadlessSurfaceCreateFlagsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class HeadlessSurfaceCreateInfoEXT {
            static $gtype: GObject.GType<HeadlessSurfaceCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class LineRasterizationModeEXT {
            static $gtype: GObject.GType<LineRasterizationModeEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceLineRasterizationFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceLineRasterizationFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceLineRasterizationPropertiesEXT {
            static $gtype: GObject.GType<PhysicalDeviceLineRasterizationPropertiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineRasterizationLineStateCreateInfoEXT {
            static $gtype: GObject.GType<PipelineRasterizationLineStateCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShaderAtomicFloatFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceShaderAtomicFloatFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceHostQueryResetFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceHostQueryResetFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceIndexTypeUint8FeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceIndexTypeUint8FeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceExtendedDynamicStateFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceExtendedDynamicStateFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class HostImageCopyFlagBitsEXT {
            static $gtype: GObject.GType<HostImageCopyFlagBitsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class HostImageCopyFlagsEXT {
            static $gtype: GObject.GType<HostImageCopyFlagsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceHostImageCopyFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceHostImageCopyFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceHostImageCopyPropertiesEXT {
            static $gtype: GObject.GType<PhysicalDeviceHostImageCopyPropertiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MemoryToImageCopyEXT {
            static $gtype: GObject.GType<MemoryToImageCopyEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageToMemoryCopyEXT {
            static $gtype: GObject.GType<ImageToMemoryCopyEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CopyMemoryToImageInfoEXT {
            static $gtype: GObject.GType<CopyMemoryToImageInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CopyImageToMemoryInfoEXT {
            static $gtype: GObject.GType<CopyImageToMemoryInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CopyImageToImageInfoEXT {
            static $gtype: GObject.GType<CopyImageToImageInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class HostImageLayoutTransitionInfoEXT {
            static $gtype: GObject.GType<HostImageLayoutTransitionInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SubresourceHostMemcpySizeEXT {
            static $gtype: GObject.GType<SubresourceHostMemcpySizeEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class HostImageCopyDevicePerformanceQueryEXT {
            static $gtype: GObject.GType<HostImageCopyDevicePerformanceQueryEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SubresourceLayout2EXT {
            static $gtype: GObject.GType<SubresourceLayout2EXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageSubresource2EXT {
            static $gtype: GObject.GType<ImageSubresource2EXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceMapMemoryPlacedFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceMapMemoryPlacedFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceMapMemoryPlacedPropertiesEXT {
            static $gtype: GObject.GType<PhysicalDeviceMapMemoryPlacedPropertiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MemoryMapPlacedInfoEXT {
            static $gtype: GObject.GType<MemoryMapPlacedInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShaderAtomicFloat2FeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceShaderAtomicFloat2FeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PresentScalingFlagBitsEXT {
            static $gtype: GObject.GType<PresentScalingFlagBitsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PresentScalingFlagsEXT {
            static $gtype: GObject.GType<PresentScalingFlagsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PresentGravityFlagBitsEXT {
            static $gtype: GObject.GType<PresentGravityFlagBitsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PresentGravityFlagsEXT {
            static $gtype: GObject.GType<PresentGravityFlagsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SurfacePresentModeEXT {
            static $gtype: GObject.GType<SurfacePresentModeEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SurfacePresentScalingCapabilitiesEXT {
            static $gtype: GObject.GType<SurfacePresentScalingCapabilitiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SurfacePresentModeCompatibilityEXT {
            static $gtype: GObject.GType<SurfacePresentModeCompatibilityEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceSwapchainMaintenance1FeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceSwapchainMaintenance1FeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SwapchainPresentFenceInfoEXT {
            static $gtype: GObject.GType<SwapchainPresentFenceInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SwapchainPresentModesCreateInfoEXT {
            static $gtype: GObject.GType<SwapchainPresentModesCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SwapchainPresentModeInfoEXT {
            static $gtype: GObject.GType<SwapchainPresentModeInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SwapchainPresentScalingCreateInfoEXT {
            static $gtype: GObject.GType<SwapchainPresentScalingCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ReleaseSwapchainImagesInfoEXT {
            static $gtype: GObject.GType<ReleaseSwapchainImagesInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShaderDemoteToHelperInvocationFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceShaderDemoteToHelperInvocationFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class IndirectCommandsLayoutNV {
            static $gtype: GObject.GType<IndirectCommandsLayoutNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class IndirectCommandsTokenTypeNV {
            static $gtype: GObject.GType<IndirectCommandsTokenTypeNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class IndirectStateFlagBitsNV {
            static $gtype: GObject.GType<IndirectStateFlagBitsNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class IndirectStateFlagsNV {
            static $gtype: GObject.GType<IndirectStateFlagsNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class IndirectCommandsLayoutUsageFlagBitsNV {
            static $gtype: GObject.GType<IndirectCommandsLayoutUsageFlagBitsNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class IndirectCommandsLayoutUsageFlagsNV {
            static $gtype: GObject.GType<IndirectCommandsLayoutUsageFlagsNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceDeviceGeneratedCommandsPropertiesNV {
            static $gtype: GObject.GType<PhysicalDeviceDeviceGeneratedCommandsPropertiesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceDeviceGeneratedCommandsFeaturesNV {
            static $gtype: GObject.GType<PhysicalDeviceDeviceGeneratedCommandsFeaturesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class GraphicsShaderGroupCreateInfoNV {
            static $gtype: GObject.GType<GraphicsShaderGroupCreateInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class GraphicsPipelineShaderGroupsCreateInfoNV {
            static $gtype: GObject.GType<GraphicsPipelineShaderGroupsCreateInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BindShaderGroupIndirectCommandNV {
            static $gtype: GObject.GType<BindShaderGroupIndirectCommandNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BindIndexBufferIndirectCommandNV {
            static $gtype: GObject.GType<BindIndexBufferIndirectCommandNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BindVertexBufferIndirectCommandNV {
            static $gtype: GObject.GType<BindVertexBufferIndirectCommandNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SetStateFlagsIndirectCommandNV {
            static $gtype: GObject.GType<SetStateFlagsIndirectCommandNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class IndirectCommandsStreamNV {
            static $gtype: GObject.GType<IndirectCommandsStreamNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class IndirectCommandsLayoutTokenNV {
            static $gtype: GObject.GType<IndirectCommandsLayoutTokenNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class IndirectCommandsLayoutCreateInfoNV {
            static $gtype: GObject.GType<IndirectCommandsLayoutCreateInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class GeneratedCommandsInfoNV {
            static $gtype: GObject.GType<GeneratedCommandsInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class GeneratedCommandsMemoryRequirementsInfoNV {
            static $gtype: GObject.GType<GeneratedCommandsMemoryRequirementsInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceInheritedViewportScissorFeaturesNV {
            static $gtype: GObject.GType<PhysicalDeviceInheritedViewportScissorFeaturesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CommandBufferInheritanceViewportScissorInfoNV {
            static $gtype: GObject.GType<CommandBufferInheritanceViewportScissorInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceTexelBufferAlignmentFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceTexelBufferAlignmentFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceTexelBufferAlignmentPropertiesEXT {
            static $gtype: GObject.GType<PhysicalDeviceTexelBufferAlignmentPropertiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RenderPassTransformBeginInfoQCOM {
            static $gtype: GObject.GType<RenderPassTransformBeginInfoQCOM>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CommandBufferInheritanceRenderPassTransformInfoQCOM {
            static $gtype: GObject.GType<CommandBufferInheritanceRenderPassTransformInfoQCOM>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DepthBiasRepresentationEXT {
            static $gtype: GObject.GType<DepthBiasRepresentationEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceDepthBiasControlFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceDepthBiasControlFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DepthBiasInfoEXT {
            static $gtype: GObject.GType<DepthBiasInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DepthBiasRepresentationInfoEXT {
            static $gtype: GObject.GType<DepthBiasRepresentationInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceMemoryReportEventTypeEXT {
            static $gtype: GObject.GType<DeviceMemoryReportEventTypeEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceMemoryReportFlagsEXT {
            static $gtype: GObject.GType<DeviceMemoryReportFlagsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceDeviceMemoryReportFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceDeviceMemoryReportFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceMemoryReportCallbackDataEXT {
            static $gtype: GObject.GType<DeviceMemoryReportCallbackDataEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceDeviceMemoryReportCreateInfoEXT {
            static $gtype: GObject.GType<DeviceDeviceMemoryReportCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceRobustness2FeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceRobustness2FeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceRobustness2PropertiesEXT {
            static $gtype: GObject.GType<PhysicalDeviceRobustness2PropertiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SamplerCustomBorderColorCreateInfoEXT {
            static $gtype: GObject.GType<SamplerCustomBorderColorCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceCustomBorderColorPropertiesEXT {
            static $gtype: GObject.GType<PhysicalDeviceCustomBorderColorPropertiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceCustomBorderColorFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceCustomBorderColorFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDevicePresentBarrierFeaturesNV {
            static $gtype: GObject.GType<PhysicalDevicePresentBarrierFeaturesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SurfaceCapabilitiesPresentBarrierNV {
            static $gtype: GObject.GType<SurfaceCapabilitiesPresentBarrierNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SwapchainPresentBarrierCreateInfoNV {
            static $gtype: GObject.GType<SwapchainPresentBarrierCreateInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PrivateDataSlotEXT {
            static $gtype: GObject.GType<PrivateDataSlotEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PrivateDataSlotCreateFlagsEXT {
            static $gtype: GObject.GType<PrivateDataSlotCreateFlagsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDevicePrivateDataFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDevicePrivateDataFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DevicePrivateDataCreateInfoEXT {
            static $gtype: GObject.GType<DevicePrivateDataCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PrivateDataSlotCreateInfoEXT {
            static $gtype: GObject.GType<PrivateDataSlotCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDevicePipelineCreationCacheControlFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDevicePipelineCreationCacheControlFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceDiagnosticsConfigFlagBitsNV {
            static $gtype: GObject.GType<DeviceDiagnosticsConfigFlagBitsNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceDiagnosticsConfigFlagsNV {
            static $gtype: GObject.GType<DeviceDiagnosticsConfigFlagsNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceDiagnosticsConfigFeaturesNV {
            static $gtype: GObject.GType<PhysicalDeviceDiagnosticsConfigFeaturesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceDiagnosticsConfigCreateInfoNV {
            static $gtype: GObject.GType<DeviceDiagnosticsConfigCreateInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CudaModuleNV {
            static $gtype: GObject.GType<CudaModuleNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CudaFunctionNV {
            static $gtype: GObject.GType<CudaFunctionNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CudaModuleCreateInfoNV {
            static $gtype: GObject.GType<CudaModuleCreateInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CudaFunctionCreateInfoNV {
            static $gtype: GObject.GType<CudaFunctionCreateInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CudaLaunchInfoNV {
            static $gtype: GObject.GType<CudaLaunchInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceCudaKernelLaunchFeaturesNV {
            static $gtype: GObject.GType<PhysicalDeviceCudaKernelLaunchFeaturesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceCudaKernelLaunchPropertiesNV {
            static $gtype: GObject.GType<PhysicalDeviceCudaKernelLaunchPropertiesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class QueryLowLatencySupportNV {
            static $gtype: GObject.GType<QueryLowLatencySupportNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AccelerationStructureKHR {
            static $gtype: GObject.GType<AccelerationStructureKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceDescriptorBufferPropertiesEXT {
            static $gtype: GObject.GType<PhysicalDeviceDescriptorBufferPropertiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceDescriptorBufferDensityMapPropertiesEXT {
            static $gtype: GObject.GType<PhysicalDeviceDescriptorBufferDensityMapPropertiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceDescriptorBufferFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceDescriptorBufferFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorAddressInfoEXT {
            static $gtype: GObject.GType<DescriptorAddressInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorBufferBindingInfoEXT {
            static $gtype: GObject.GType<DescriptorBufferBindingInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorBufferBindingPushDescriptorBufferHandleEXT {
            static $gtype: GObject.GType<DescriptorBufferBindingPushDescriptorBufferHandleEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorDataEXT {
            static $gtype: GObject.GType<DescriptorDataEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorGetInfoEXT {
            static $gtype: GObject.GType<DescriptorGetInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BufferCaptureDescriptorDataInfoEXT {
            static $gtype: GObject.GType<BufferCaptureDescriptorDataInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageCaptureDescriptorDataInfoEXT {
            static $gtype: GObject.GType<ImageCaptureDescriptorDataInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageViewCaptureDescriptorDataInfoEXT {
            static $gtype: GObject.GType<ImageViewCaptureDescriptorDataInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SamplerCaptureDescriptorDataInfoEXT {
            static $gtype: GObject.GType<SamplerCaptureDescriptorDataInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class OpaqueCaptureDescriptorDataCreateInfoEXT {
            static $gtype: GObject.GType<OpaqueCaptureDescriptorDataCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AccelerationStructureCaptureDescriptorDataInfoEXT {
            static $gtype: GObject.GType<AccelerationStructureCaptureDescriptorDataInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class GraphicsPipelineLibraryFlagBitsEXT {
            static $gtype: GObject.GType<GraphicsPipelineLibraryFlagBitsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class GraphicsPipelineLibraryFlagsEXT {
            static $gtype: GObject.GType<GraphicsPipelineLibraryFlagsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceGraphicsPipelineLibraryFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceGraphicsPipelineLibraryFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceGraphicsPipelineLibraryPropertiesEXT {
            static $gtype: GObject.GType<PhysicalDeviceGraphicsPipelineLibraryPropertiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class GraphicsPipelineLibraryCreateInfoEXT {
            static $gtype: GObject.GType<GraphicsPipelineLibraryCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShaderEarlyAndLateFragmentTestsFeaturesAMD {
            static $gtype: GObject.GType<PhysicalDeviceShaderEarlyAndLateFragmentTestsFeaturesAMD>;

            // Constructors

            _init(...args: any[]): void;
        }

        class FragmentShadingRateTypeNV {
            static $gtype: GObject.GType<FragmentShadingRateTypeNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class FragmentShadingRateNV {
            static $gtype: GObject.GType<FragmentShadingRateNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceFragmentShadingRateEnumsFeaturesNV {
            static $gtype: GObject.GType<PhysicalDeviceFragmentShadingRateEnumsFeaturesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceFragmentShadingRateEnumsPropertiesNV {
            static $gtype: GObject.GType<PhysicalDeviceFragmentShadingRateEnumsPropertiesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineFragmentShadingRateEnumStateCreateInfoNV {
            static $gtype: GObject.GType<PipelineFragmentShadingRateEnumStateCreateInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AccelerationStructureMotionInstanceTypeNV {
            static $gtype: GObject.GType<AccelerationStructureMotionInstanceTypeNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AccelerationStructureMotionInfoFlagsNV {
            static $gtype: GObject.GType<AccelerationStructureMotionInfoFlagsNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AccelerationStructureMotionInstanceFlagsNV {
            static $gtype: GObject.GType<AccelerationStructureMotionInstanceFlagsNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceOrHostAddressConstKHR {
            static $gtype: GObject.GType<DeviceOrHostAddressConstKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AccelerationStructureGeometryMotionTrianglesDataNV {
            static $gtype: GObject.GType<AccelerationStructureGeometryMotionTrianglesDataNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AccelerationStructureMotionInfoNV {
            static $gtype: GObject.GType<AccelerationStructureMotionInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AccelerationStructureMatrixMotionInstanceNV {
            static $gtype: GObject.GType<AccelerationStructureMatrixMotionInstanceNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SRTDataNV {
            static $gtype: GObject.GType<SRTDataNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AccelerationStructureSRTMotionInstanceNV {
            static $gtype: GObject.GType<AccelerationStructureSRTMotionInstanceNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AccelerationStructureMotionInstanceDataNV {
            static $gtype: GObject.GType<AccelerationStructureMotionInstanceDataNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AccelerationStructureMotionInstanceNV {
            static $gtype: GObject.GType<AccelerationStructureMotionInstanceNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceRayTracingMotionBlurFeaturesNV {
            static $gtype: GObject.GType<PhysicalDeviceRayTracingMotionBlurFeaturesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceYcbcr2Plane444FormatsFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceYcbcr2Plane444FormatsFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceFragmentDensityMap2FeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceFragmentDensityMap2FeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceFragmentDensityMap2PropertiesEXT {
            static $gtype: GObject.GType<PhysicalDeviceFragmentDensityMap2PropertiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CopyCommandTransformInfoQCOM {
            static $gtype: GObject.GType<CopyCommandTransformInfoQCOM>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceImageRobustnessFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceImageRobustnessFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageCompressionFlagBitsEXT {
            static $gtype: GObject.GType<ImageCompressionFlagBitsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageCompressionFlagsEXT {
            static $gtype: GObject.GType<ImageCompressionFlagsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageCompressionFixedRateFlagBitsEXT {
            static $gtype: GObject.GType<ImageCompressionFixedRateFlagBitsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageCompressionFixedRateFlagsEXT {
            static $gtype: GObject.GType<ImageCompressionFixedRateFlagsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceImageCompressionControlFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceImageCompressionControlFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageCompressionControlEXT {
            static $gtype: GObject.GType<ImageCompressionControlEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageCompressionPropertiesEXT {
            static $gtype: GObject.GType<ImageCompressionPropertiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceAttachmentFeedbackLoopLayoutFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceAttachmentFeedbackLoopLayoutFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDevice4444FormatsFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDevice4444FormatsFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceFaultAddressTypeEXT {
            static $gtype: GObject.GType<DeviceFaultAddressTypeEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceFaultVendorBinaryHeaderVersionEXT {
            static $gtype: GObject.GType<DeviceFaultVendorBinaryHeaderVersionEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceFaultFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceFaultFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceFaultCountsEXT {
            static $gtype: GObject.GType<DeviceFaultCountsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceFaultAddressInfoEXT {
            static $gtype: GObject.GType<DeviceFaultAddressInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceFaultVendorInfoEXT {
            static $gtype: GObject.GType<DeviceFaultVendorInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceFaultInfoEXT {
            static $gtype: GObject.GType<DeviceFaultInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceFaultVendorBinaryHeaderVersionOneEXT {
            static $gtype: GObject.GType<DeviceFaultVendorBinaryHeaderVersionOneEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceRasterizationOrderAttachmentAccessFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceRasterizationOrderAttachmentAccessFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceRasterizationOrderAttachmentAccessFeaturesARM {
            static $gtype: GObject.GType<PhysicalDeviceRasterizationOrderAttachmentAccessFeaturesARM>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceRGBA10X6FormatsFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceRGBA10X6FormatsFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceMutableDescriptorTypeFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceMutableDescriptorTypeFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceMutableDescriptorTypeFeaturesVALVE {
            static $gtype: GObject.GType<PhysicalDeviceMutableDescriptorTypeFeaturesVALVE>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MutableDescriptorTypeListEXT {
            static $gtype: GObject.GType<MutableDescriptorTypeListEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MutableDescriptorTypeListVALVE {
            static $gtype: GObject.GType<MutableDescriptorTypeListVALVE>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MutableDescriptorTypeCreateInfoEXT {
            static $gtype: GObject.GType<MutableDescriptorTypeCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MutableDescriptorTypeCreateInfoVALVE {
            static $gtype: GObject.GType<MutableDescriptorTypeCreateInfoVALVE>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceVertexInputDynamicStateFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceVertexInputDynamicStateFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VertexInputBindingDescription2EXT {
            static $gtype: GObject.GType<VertexInputBindingDescription2EXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class VertexInputAttributeDescription2EXT {
            static $gtype: GObject.GType<VertexInputAttributeDescription2EXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceDrmPropertiesEXT {
            static $gtype: GObject.GType<PhysicalDeviceDrmPropertiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceAddressBindingTypeEXT {
            static $gtype: GObject.GType<DeviceAddressBindingTypeEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceAddressBindingFlagBitsEXT {
            static $gtype: GObject.GType<DeviceAddressBindingFlagBitsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceAddressBindingFlagsEXT {
            static $gtype: GObject.GType<DeviceAddressBindingFlagsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceAddressBindingReportFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceAddressBindingReportFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceAddressBindingCallbackDataEXT {
            static $gtype: GObject.GType<DeviceAddressBindingCallbackDataEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceDepthClipControlFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceDepthClipControlFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineViewportDepthClipControlCreateInfoEXT {
            static $gtype: GObject.GType<PipelineViewportDepthClipControlCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDevicePrimitiveTopologyListRestartFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDevicePrimitiveTopologyListRestartFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SubpassShadingPipelineCreateInfoHUAWEI {
            static $gtype: GObject.GType<SubpassShadingPipelineCreateInfoHUAWEI>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceSubpassShadingFeaturesHUAWEI {
            static $gtype: GObject.GType<PhysicalDeviceSubpassShadingFeaturesHUAWEI>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceSubpassShadingPropertiesHUAWEI {
            static $gtype: GObject.GType<PhysicalDeviceSubpassShadingPropertiesHUAWEI>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceInvocationMaskFeaturesHUAWEI {
            static $gtype: GObject.GType<PhysicalDeviceInvocationMaskFeaturesHUAWEI>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RemoteAddressNV {
            static $gtype: GObject.GType<RemoteAddressNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MemoryGetRemoteAddressInfoNV {
            static $gtype: GObject.GType<MemoryGetRemoteAddressInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceExternalMemoryRDMAFeaturesNV {
            static $gtype: GObject.GType<PhysicalDeviceExternalMemoryRDMAFeaturesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineInfoEXT {
            static $gtype: GObject.GType<PipelineInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelinePropertiesIdentifierEXT {
            static $gtype: GObject.GType<PipelinePropertiesIdentifierEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDevicePipelinePropertiesFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDevicePipelinePropertiesFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class FrameBoundaryFlagBitsEXT {
            static $gtype: GObject.GType<FrameBoundaryFlagBitsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class FrameBoundaryFlagsEXT {
            static $gtype: GObject.GType<FrameBoundaryFlagsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceFrameBoundaryFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceFrameBoundaryFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class FrameBoundaryEXT {
            static $gtype: GObject.GType<FrameBoundaryEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceMultisampledRenderToSingleSampledFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceMultisampledRenderToSingleSampledFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SubpassResolvePerformanceQueryEXT {
            static $gtype: GObject.GType<SubpassResolvePerformanceQueryEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MultisampledRenderToSingleSampledInfoEXT {
            static $gtype: GObject.GType<MultisampledRenderToSingleSampledInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceExtendedDynamicState2FeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceExtendedDynamicState2FeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceColorWriteEnableFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceColorWriteEnableFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineColorWriteCreateInfoEXT {
            static $gtype: GObject.GType<PipelineColorWriteCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDevicePrimitivesGeneratedQueryFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDevicePrimitivesGeneratedQueryFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceGlobalPriorityQueryFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceGlobalPriorityQueryFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class QueueFamilyGlobalPriorityPropertiesEXT {
            static $gtype: GObject.GType<QueueFamilyGlobalPriorityPropertiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceImageViewMinLodFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceImageViewMinLodFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageViewMinLodCreateInfoEXT {
            static $gtype: GObject.GType<ImageViewMinLodCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceMultiDrawFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceMultiDrawFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceMultiDrawPropertiesEXT {
            static $gtype: GObject.GType<PhysicalDeviceMultiDrawPropertiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MultiDrawInfoEXT {
            static $gtype: GObject.GType<MultiDrawInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MultiDrawIndexedInfoEXT {
            static $gtype: GObject.GType<MultiDrawIndexedInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceImage2DViewOf3DFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceImage2DViewOf3DFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShaderTileImageFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceShaderTileImageFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShaderTileImagePropertiesEXT {
            static $gtype: GObject.GType<PhysicalDeviceShaderTileImagePropertiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MicromapEXT {
            static $gtype: GObject.GType<MicromapEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MicromapTypeEXT {
            static $gtype: GObject.GType<MicromapTypeEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BuildMicromapModeEXT {
            static $gtype: GObject.GType<BuildMicromapModeEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CopyMicromapModeEXT {
            static $gtype: GObject.GType<CopyMicromapModeEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class OpacityMicromapFormatEXT {
            static $gtype: GObject.GType<OpacityMicromapFormatEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class OpacityMicromapSpecialIndexEXT {
            static $gtype: GObject.GType<OpacityMicromapSpecialIndexEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AccelerationStructureCompatibilityKHR {
            static $gtype: GObject.GType<AccelerationStructureCompatibilityKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AccelerationStructureBuildTypeKHR {
            static $gtype: GObject.GType<AccelerationStructureBuildTypeKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BuildMicromapFlagBitsEXT {
            static $gtype: GObject.GType<BuildMicromapFlagBitsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BuildMicromapFlagsEXT {
            static $gtype: GObject.GType<BuildMicromapFlagsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MicromapCreateFlagBitsEXT {
            static $gtype: GObject.GType<MicromapCreateFlagBitsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MicromapCreateFlagsEXT {
            static $gtype: GObject.GType<MicromapCreateFlagsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MicromapUsageEXT {
            static $gtype: GObject.GType<MicromapUsageEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceOrHostAddressKHR {
            static $gtype: GObject.GType<DeviceOrHostAddressKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MicromapBuildInfoEXT {
            static $gtype: GObject.GType<MicromapBuildInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MicromapCreateInfoEXT {
            static $gtype: GObject.GType<MicromapCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceOpacityMicromapFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceOpacityMicromapFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceOpacityMicromapPropertiesEXT {
            static $gtype: GObject.GType<PhysicalDeviceOpacityMicromapPropertiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MicromapVersionInfoEXT {
            static $gtype: GObject.GType<MicromapVersionInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CopyMicromapToMemoryInfoEXT {
            static $gtype: GObject.GType<CopyMicromapToMemoryInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CopyMemoryToMicromapInfoEXT {
            static $gtype: GObject.GType<CopyMemoryToMicromapInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CopyMicromapInfoEXT {
            static $gtype: GObject.GType<CopyMicromapInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MicromapBuildSizesInfoEXT {
            static $gtype: GObject.GType<MicromapBuildSizesInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AccelerationStructureTrianglesOpacityMicromapEXT {
            static $gtype: GObject.GType<AccelerationStructureTrianglesOpacityMicromapEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MicromapTriangleEXT {
            static $gtype: GObject.GType<MicromapTriangleEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceClusterCullingShaderFeaturesHUAWEI {
            static $gtype: GObject.GType<PhysicalDeviceClusterCullingShaderFeaturesHUAWEI>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceClusterCullingShaderPropertiesHUAWEI {
            static $gtype: GObject.GType<PhysicalDeviceClusterCullingShaderPropertiesHUAWEI>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceClusterCullingShaderVrsFeaturesHUAWEI {
            static $gtype: GObject.GType<PhysicalDeviceClusterCullingShaderVrsFeaturesHUAWEI>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceBorderColorSwizzleFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceBorderColorSwizzleFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SamplerBorderColorComponentMappingCreateInfoEXT {
            static $gtype: GObject.GType<SamplerBorderColorComponentMappingCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDevicePageableDeviceLocalMemoryFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDevicePageableDeviceLocalMemoryFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShaderCorePropertiesARM {
            static $gtype: GObject.GType<PhysicalDeviceShaderCorePropertiesARM>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceSchedulingControlsFlagsARM {
            static $gtype: GObject.GType<PhysicalDeviceSchedulingControlsFlagsARM>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceSchedulingControlsFlagBitsARM {
            static $gtype: GObject.GType<PhysicalDeviceSchedulingControlsFlagBitsARM>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DeviceQueueShaderCoreControlCreateInfoARM {
            static $gtype: GObject.GType<DeviceQueueShaderCoreControlCreateInfoARM>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceSchedulingControlsFeaturesARM {
            static $gtype: GObject.GType<PhysicalDeviceSchedulingControlsFeaturesARM>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceSchedulingControlsPropertiesARM {
            static $gtype: GObject.GType<PhysicalDeviceSchedulingControlsPropertiesARM>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceImageSlicedViewOf3DFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceImageSlicedViewOf3DFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageViewSlicedCreateInfoEXT {
            static $gtype: GObject.GType<ImageViewSlicedCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceDescriptorSetHostMappingFeaturesVALVE {
            static $gtype: GObject.GType<PhysicalDeviceDescriptorSetHostMappingFeaturesVALVE>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorSetBindingReferenceVALVE {
            static $gtype: GObject.GType<DescriptorSetBindingReferenceVALVE>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DescriptorSetLayoutHostMappingInfoVALVE {
            static $gtype: GObject.GType<DescriptorSetLayoutHostMappingInfoVALVE>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceDepthClampZeroOneFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceDepthClampZeroOneFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceNonSeamlessCubeMapFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceNonSeamlessCubeMapFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceRenderPassStripedFeaturesARM {
            static $gtype: GObject.GType<PhysicalDeviceRenderPassStripedFeaturesARM>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceRenderPassStripedPropertiesARM {
            static $gtype: GObject.GType<PhysicalDeviceRenderPassStripedPropertiesARM>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RenderPassStripeInfoARM {
            static $gtype: GObject.GType<RenderPassStripeInfoARM>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RenderPassStripeBeginInfoARM {
            static $gtype: GObject.GType<RenderPassStripeBeginInfoARM>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RenderPassStripeSubmitInfoARM {
            static $gtype: GObject.GType<RenderPassStripeSubmitInfoARM>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceFragmentDensityMapOffsetFeaturesQCOM {
            static $gtype: GObject.GType<PhysicalDeviceFragmentDensityMapOffsetFeaturesQCOM>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceFragmentDensityMapOffsetPropertiesQCOM {
            static $gtype: GObject.GType<PhysicalDeviceFragmentDensityMapOffsetPropertiesQCOM>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SubpassFragmentDensityMapOffsetEndInfoQCOM {
            static $gtype: GObject.GType<SubpassFragmentDensityMapOffsetEndInfoQCOM>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CopyMemoryIndirectCommandNV {
            static $gtype: GObject.GType<CopyMemoryIndirectCommandNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CopyMemoryToImageIndirectCommandNV {
            static $gtype: GObject.GType<CopyMemoryToImageIndirectCommandNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceCopyMemoryIndirectFeaturesNV {
            static $gtype: GObject.GType<PhysicalDeviceCopyMemoryIndirectFeaturesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceCopyMemoryIndirectPropertiesNV {
            static $gtype: GObject.GType<PhysicalDeviceCopyMemoryIndirectPropertiesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MemoryDecompressionMethodFlagBitsNV {
            static $gtype: GObject.GType<MemoryDecompressionMethodFlagBitsNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MemoryDecompressionMethodFlagsNV {
            static $gtype: GObject.GType<MemoryDecompressionMethodFlagsNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DecompressMemoryRegionNV {
            static $gtype: GObject.GType<DecompressMemoryRegionNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceMemoryDecompressionFeaturesNV {
            static $gtype: GObject.GType<PhysicalDeviceMemoryDecompressionFeaturesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceMemoryDecompressionPropertiesNV {
            static $gtype: GObject.GType<PhysicalDeviceMemoryDecompressionPropertiesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceDeviceGeneratedCommandsComputeFeaturesNV {
            static $gtype: GObject.GType<PhysicalDeviceDeviceGeneratedCommandsComputeFeaturesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ComputePipelineIndirectBufferInfoNV {
            static $gtype: GObject.GType<ComputePipelineIndirectBufferInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineIndirectDeviceAddressInfoNV {
            static $gtype: GObject.GType<PipelineIndirectDeviceAddressInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BindPipelineIndirectCommandNV {
            static $gtype: GObject.GType<BindPipelineIndirectCommandNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceLinearColorAttachmentFeaturesNV {
            static $gtype: GObject.GType<PhysicalDeviceLinearColorAttachmentFeaturesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceImageCompressionControlSwapchainFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceImageCompressionControlSwapchainFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageViewSampleWeightCreateInfoQCOM {
            static $gtype: GObject.GType<ImageViewSampleWeightCreateInfoQCOM>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceImageProcessingFeaturesQCOM {
            static $gtype: GObject.GType<PhysicalDeviceImageProcessingFeaturesQCOM>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceImageProcessingPropertiesQCOM {
            static $gtype: GObject.GType<PhysicalDeviceImageProcessingPropertiesQCOM>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceNestedCommandBufferFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceNestedCommandBufferFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceNestedCommandBufferPropertiesEXT {
            static $gtype: GObject.GType<PhysicalDeviceNestedCommandBufferPropertiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ExternalMemoryAcquireUnmodifiedEXT {
            static $gtype: GObject.GType<ExternalMemoryAcquireUnmodifiedEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceExtendedDynamicState3FeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceExtendedDynamicState3FeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceExtendedDynamicState3PropertiesEXT {
            static $gtype: GObject.GType<PhysicalDeviceExtendedDynamicState3PropertiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ColorBlendEquationEXT {
            static $gtype: GObject.GType<ColorBlendEquationEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ColorBlendAdvancedEXT {
            static $gtype: GObject.GType<ColorBlendAdvancedEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SubpassMergeStatusEXT {
            static $gtype: GObject.GType<SubpassMergeStatusEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceSubpassMergeFeedbackFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceSubpassMergeFeedbackFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RenderPassCreationControlEXT {
            static $gtype: GObject.GType<RenderPassCreationControlEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RenderPassCreationFeedbackInfoEXT {
            static $gtype: GObject.GType<RenderPassCreationFeedbackInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RenderPassCreationFeedbackCreateInfoEXT {
            static $gtype: GObject.GType<RenderPassCreationFeedbackCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RenderPassSubpassFeedbackInfoEXT {
            static $gtype: GObject.GType<RenderPassSubpassFeedbackInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RenderPassSubpassFeedbackCreateInfoEXT {
            static $gtype: GObject.GType<RenderPassSubpassFeedbackCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DirectDriverLoadingModeLUNARG {
            static $gtype: GObject.GType<DirectDriverLoadingModeLUNARG>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DirectDriverLoadingFlagsLUNARG {
            static $gtype: GObject.GType<DirectDriverLoadingFlagsLUNARG>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DirectDriverLoadingInfoLUNARG {
            static $gtype: GObject.GType<DirectDriverLoadingInfoLUNARG>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DirectDriverLoadingListLUNARG {
            static $gtype: GObject.GType<DirectDriverLoadingListLUNARG>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShaderModuleIdentifierFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceShaderModuleIdentifierFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShaderModuleIdentifierPropertiesEXT {
            static $gtype: GObject.GType<PhysicalDeviceShaderModuleIdentifierPropertiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PipelineShaderStageModuleIdentifierCreateInfoEXT {
            static $gtype: GObject.GType<PipelineShaderStageModuleIdentifierCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ShaderModuleIdentifierEXT {
            static $gtype: GObject.GType<ShaderModuleIdentifierEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class OpticalFlowSessionNV {
            static $gtype: GObject.GType<OpticalFlowSessionNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class OpticalFlowPerformanceLevelNV {
            static $gtype: GObject.GType<OpticalFlowPerformanceLevelNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class OpticalFlowSessionBindingPointNV {
            static $gtype: GObject.GType<OpticalFlowSessionBindingPointNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class OpticalFlowGridSizeFlagBitsNV {
            static $gtype: GObject.GType<OpticalFlowGridSizeFlagBitsNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class OpticalFlowGridSizeFlagsNV {
            static $gtype: GObject.GType<OpticalFlowGridSizeFlagsNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class OpticalFlowUsageFlagBitsNV {
            static $gtype: GObject.GType<OpticalFlowUsageFlagBitsNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class OpticalFlowUsageFlagsNV {
            static $gtype: GObject.GType<OpticalFlowUsageFlagsNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class OpticalFlowSessionCreateFlagBitsNV {
            static $gtype: GObject.GType<OpticalFlowSessionCreateFlagBitsNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class OpticalFlowSessionCreateFlagsNV {
            static $gtype: GObject.GType<OpticalFlowSessionCreateFlagsNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class OpticalFlowExecuteFlagBitsNV {
            static $gtype: GObject.GType<OpticalFlowExecuteFlagBitsNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class OpticalFlowExecuteFlagsNV {
            static $gtype: GObject.GType<OpticalFlowExecuteFlagsNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceOpticalFlowFeaturesNV {
            static $gtype: GObject.GType<PhysicalDeviceOpticalFlowFeaturesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceOpticalFlowPropertiesNV {
            static $gtype: GObject.GType<PhysicalDeviceOpticalFlowPropertiesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class OpticalFlowImageFormatInfoNV {
            static $gtype: GObject.GType<OpticalFlowImageFormatInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class OpticalFlowImageFormatPropertiesNV {
            static $gtype: GObject.GType<OpticalFlowImageFormatPropertiesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class OpticalFlowSessionCreateInfoNV {
            static $gtype: GObject.GType<OpticalFlowSessionCreateInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class OpticalFlowSessionCreatePrivateDataInfoNV {
            static $gtype: GObject.GType<OpticalFlowSessionCreatePrivateDataInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class OpticalFlowExecuteInfoNV {
            static $gtype: GObject.GType<OpticalFlowExecuteInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceLegacyDitheringFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceLegacyDitheringFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDevicePipelineProtectedAccessFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDevicePipelineProtectedAccessFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ShaderEXT {
            static $gtype: GObject.GType<ShaderEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ShaderCodeTypeEXT {
            static $gtype: GObject.GType<ShaderCodeTypeEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ShaderCreateFlagBitsEXT {
            static $gtype: GObject.GType<ShaderCreateFlagBitsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ShaderCreateFlagsEXT {
            static $gtype: GObject.GType<ShaderCreateFlagsEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShaderObjectFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceShaderObjectFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShaderObjectPropertiesEXT {
            static $gtype: GObject.GType<PhysicalDeviceShaderObjectPropertiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ShaderCreateInfoEXT {
            static $gtype: GObject.GType<ShaderCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ShaderRequiredSubgroupSizeCreateInfoEXT {
            static $gtype: GObject.GType<ShaderRequiredSubgroupSizeCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceTilePropertiesFeaturesQCOM {
            static $gtype: GObject.GType<PhysicalDeviceTilePropertiesFeaturesQCOM>;

            // Constructors

            _init(...args: any[]): void;
        }

        class TilePropertiesQCOM {
            static $gtype: GObject.GType<TilePropertiesQCOM>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceAmigoProfilingFeaturesSEC {
            static $gtype: GObject.GType<PhysicalDeviceAmigoProfilingFeaturesSEC>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AmigoProfilingSubmitInfoSEC {
            static $gtype: GObject.GType<AmigoProfilingSubmitInfoSEC>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceMultiviewPerViewViewportsFeaturesQCOM {
            static $gtype: GObject.GType<PhysicalDeviceMultiviewPerViewViewportsFeaturesQCOM>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RayTracingInvocationReorderModeNV {
            static $gtype: GObject.GType<RayTracingInvocationReorderModeNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceRayTracingInvocationReorderPropertiesNV {
            static $gtype: GObject.GType<PhysicalDeviceRayTracingInvocationReorderPropertiesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceRayTracingInvocationReorderFeaturesNV {
            static $gtype: GObject.GType<PhysicalDeviceRayTracingInvocationReorderFeaturesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceExtendedSparseAddressSpaceFeaturesNV {
            static $gtype: GObject.GType<PhysicalDeviceExtendedSparseAddressSpaceFeaturesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceExtendedSparseAddressSpacePropertiesNV {
            static $gtype: GObject.GType<PhysicalDeviceExtendedSparseAddressSpacePropertiesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceLegacyVertexAttributesFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceLegacyVertexAttributesFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceLegacyVertexAttributesPropertiesEXT {
            static $gtype: GObject.GType<PhysicalDeviceLegacyVertexAttributesPropertiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class LayerSettingTypeEXT {
            static $gtype: GObject.GType<LayerSettingTypeEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class LayerSettingEXT {
            static $gtype: GObject.GType<LayerSettingEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class LayerSettingsCreateInfoEXT {
            static $gtype: GObject.GType<LayerSettingsCreateInfoEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShaderCoreBuiltinsFeaturesARM {
            static $gtype: GObject.GType<PhysicalDeviceShaderCoreBuiltinsFeaturesARM>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShaderCoreBuiltinsPropertiesARM {
            static $gtype: GObject.GType<PhysicalDeviceShaderCoreBuiltinsPropertiesARM>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDevicePipelineLibraryGroupHandlesFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDevicePipelineLibraryGroupHandlesFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceDynamicRenderingUnusedAttachmentsFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceDynamicRenderingUnusedAttachmentsFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class LatencyMarkerNV {
            static $gtype: GObject.GType<LatencyMarkerNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class OutOfBandQueueTypeNV {
            static $gtype: GObject.GType<OutOfBandQueueTypeNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class LatencySleepModeInfoNV {
            static $gtype: GObject.GType<LatencySleepModeInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class LatencySleepInfoNV {
            static $gtype: GObject.GType<LatencySleepInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SetLatencyMarkerInfoNV {
            static $gtype: GObject.GType<SetLatencyMarkerInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class LatencyTimingsFrameReportNV {
            static $gtype: GObject.GType<LatencyTimingsFrameReportNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class GetLatencyMarkerInfoNV {
            static $gtype: GObject.GType<GetLatencyMarkerInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class LatencySubmissionPresentIdNV {
            static $gtype: GObject.GType<LatencySubmissionPresentIdNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SwapchainLatencyCreateInfoNV {
            static $gtype: GObject.GType<SwapchainLatencyCreateInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class OutOfBandQueueTypeInfoNV {
            static $gtype: GObject.GType<OutOfBandQueueTypeInfoNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class LatencySurfaceCapabilitiesNV {
            static $gtype: GObject.GType<LatencySurfaceCapabilitiesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceMultiviewPerViewRenderAreasFeaturesQCOM {
            static $gtype: GObject.GType<PhysicalDeviceMultiviewPerViewRenderAreasFeaturesQCOM>;

            // Constructors

            _init(...args: any[]): void;
        }

        class MultiviewPerViewRenderAreasRenderPassBeginInfoQCOM {
            static $gtype: GObject.GType<MultiviewPerViewRenderAreasRenderPassBeginInfoQCOM>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDevicePerStageDescriptorSetFeaturesNV {
            static $gtype: GObject.GType<PhysicalDevicePerStageDescriptorSetFeaturesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BlockMatchWindowCompareModeQCOM {
            static $gtype: GObject.GType<BlockMatchWindowCompareModeQCOM>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceImageProcessing2FeaturesQCOM {
            static $gtype: GObject.GType<PhysicalDeviceImageProcessing2FeaturesQCOM>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceImageProcessing2PropertiesQCOM {
            static $gtype: GObject.GType<PhysicalDeviceImageProcessing2PropertiesQCOM>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SamplerBlockMatchWindowCreateInfoQCOM {
            static $gtype: GObject.GType<SamplerBlockMatchWindowCreateInfoQCOM>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CubicFilterWeightsQCOM {
            static $gtype: GObject.GType<CubicFilterWeightsQCOM>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceCubicWeightsFeaturesQCOM {
            static $gtype: GObject.GType<PhysicalDeviceCubicWeightsFeaturesQCOM>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SamplerCubicWeightsCreateInfoQCOM {
            static $gtype: GObject.GType<SamplerCubicWeightsCreateInfoQCOM>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BlitImageCubicWeightsInfoQCOM {
            static $gtype: GObject.GType<BlitImageCubicWeightsInfoQCOM>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceYcbcrDegammaFeaturesQCOM {
            static $gtype: GObject.GType<PhysicalDeviceYcbcrDegammaFeaturesQCOM>;

            // Constructors

            _init(...args: any[]): void;
        }

        class SamplerYcbcrConversionYcbcrDegammaCreateInfoQCOM {
            static $gtype: GObject.GType<SamplerYcbcrConversionYcbcrDegammaCreateInfoQCOM>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceCubicClampFeaturesQCOM {
            static $gtype: GObject.GType<PhysicalDeviceCubicClampFeaturesQCOM>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceAttachmentFeedbackLoopDynamicStateFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceAttachmentFeedbackLoopDynamicStateFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class LayeredDriverUnderlyingApiMSFT {
            static $gtype: GObject.GType<LayeredDriverUnderlyingApiMSFT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceLayeredDriverPropertiesMSFT {
            static $gtype: GObject.GType<PhysicalDeviceLayeredDriverPropertiesMSFT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceDescriptorPoolOverallocationFeaturesNV {
            static $gtype: GObject.GType<PhysicalDeviceDescriptorPoolOverallocationFeaturesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceRawAccessChainsFeaturesNV {
            static $gtype: GObject.GType<PhysicalDeviceRawAccessChainsFeaturesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceShaderAtomicFloat16VectorFeaturesNV {
            static $gtype: GObject.GType<PhysicalDeviceShaderAtomicFloat16VectorFeaturesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceRayTracingValidationFeaturesNV {
            static $gtype: GObject.GType<PhysicalDeviceRayTracingValidationFeaturesNV>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceImageAlignmentControlFeaturesMESA {
            static $gtype: GObject.GType<PhysicalDeviceImageAlignmentControlFeaturesMESA>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceImageAlignmentControlPropertiesMESA {
            static $gtype: GObject.GType<PhysicalDeviceImageAlignmentControlPropertiesMESA>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ImageAlignmentControlCreateInfoMESA {
            static $gtype: GObject.GType<ImageAlignmentControlCreateInfoMESA>;

            // Constructors

            _init(...args: any[]): void;
        }

        class BuildAccelerationStructureModeKHR {
            static $gtype: GObject.GType<BuildAccelerationStructureModeKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AccelerationStructureCreateFlagBitsKHR {
            static $gtype: GObject.GType<AccelerationStructureCreateFlagBitsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AccelerationStructureCreateFlagsKHR {
            static $gtype: GObject.GType<AccelerationStructureCreateFlagsKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AccelerationStructureBuildRangeInfoKHR {
            static $gtype: GObject.GType<AccelerationStructureBuildRangeInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AccelerationStructureGeometryTrianglesDataKHR {
            static $gtype: GObject.GType<AccelerationStructureGeometryTrianglesDataKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AccelerationStructureGeometryAabbsDataKHR {
            static $gtype: GObject.GType<AccelerationStructureGeometryAabbsDataKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AccelerationStructureGeometryInstancesDataKHR {
            static $gtype: GObject.GType<AccelerationStructureGeometryInstancesDataKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AccelerationStructureGeometryDataKHR {
            static $gtype: GObject.GType<AccelerationStructureGeometryDataKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AccelerationStructureGeometryKHR {
            static $gtype: GObject.GType<AccelerationStructureGeometryKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AccelerationStructureBuildGeometryInfoKHR {
            static $gtype: GObject.GType<AccelerationStructureBuildGeometryInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AccelerationStructureCreateInfoKHR {
            static $gtype: GObject.GType<AccelerationStructureCreateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class WriteDescriptorSetAccelerationStructureKHR {
            static $gtype: GObject.GType<WriteDescriptorSetAccelerationStructureKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceAccelerationStructureFeaturesKHR {
            static $gtype: GObject.GType<PhysicalDeviceAccelerationStructureFeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceAccelerationStructurePropertiesKHR {
            static $gtype: GObject.GType<PhysicalDeviceAccelerationStructurePropertiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AccelerationStructureDeviceAddressInfoKHR {
            static $gtype: GObject.GType<AccelerationStructureDeviceAddressInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AccelerationStructureVersionInfoKHR {
            static $gtype: GObject.GType<AccelerationStructureVersionInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CopyAccelerationStructureToMemoryInfoKHR {
            static $gtype: GObject.GType<CopyAccelerationStructureToMemoryInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CopyMemoryToAccelerationStructureInfoKHR {
            static $gtype: GObject.GType<CopyMemoryToAccelerationStructureInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class CopyAccelerationStructureInfoKHR {
            static $gtype: GObject.GType<CopyAccelerationStructureInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class AccelerationStructureBuildSizesInfoKHR {
            static $gtype: GObject.GType<AccelerationStructureBuildSizesInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class ShaderGroupShaderKHR {
            static $gtype: GObject.GType<ShaderGroupShaderKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RayTracingShaderGroupCreateInfoKHR {
            static $gtype: GObject.GType<RayTracingShaderGroupCreateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RayTracingPipelineInterfaceCreateInfoKHR {
            static $gtype: GObject.GType<RayTracingPipelineInterfaceCreateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class RayTracingPipelineCreateInfoKHR {
            static $gtype: GObject.GType<RayTracingPipelineCreateInfoKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceRayTracingPipelineFeaturesKHR {
            static $gtype: GObject.GType<PhysicalDeviceRayTracingPipelineFeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceRayTracingPipelinePropertiesKHR {
            static $gtype: GObject.GType<PhysicalDeviceRayTracingPipelinePropertiesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class StridedDeviceAddressRegionKHR {
            static $gtype: GObject.GType<StridedDeviceAddressRegionKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class TraceRaysIndirectCommandKHR {
            static $gtype: GObject.GType<TraceRaysIndirectCommandKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceRayQueryFeaturesKHR {
            static $gtype: GObject.GType<PhysicalDeviceRayQueryFeaturesKHR>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceMeshShaderFeaturesEXT {
            static $gtype: GObject.GType<PhysicalDeviceMeshShaderFeaturesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class PhysicalDeviceMeshShaderPropertiesEXT {
            static $gtype: GObject.GType<PhysicalDeviceMeshShaderPropertiesEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        class DrawMeshTasksIndirectCommandEXT {
            static $gtype: GObject.GType<DrawMeshTasksIndirectCommandEXT>;

            // Constructors

            _init(...args: any[]): void;
        }

        /**
         * Name of the imported GIR library
         * `see` https://gitlab.gnome.org/GNOME/gjs/-/blob/master/gi/ns.cpp#L188
         */
        const __name__: string;
        /**
         * Version of the imported GIR library
         * `see` https://gitlab.gnome.org/GNOME/gjs/-/blob/master/gi/ns.cpp#L189
         */
        const __version__: string;
    }

    export default Vulkan;
}

declare module 'gi://Vulkan' {
    import Vulkan10 from 'gi://Vulkan?version=1.0';
    export default Vulkan10;
}
// END
