{"name": "faiyt-astal-shell", "version": "0.0.1", "description": "Modern UI for AGS/Hyprland", "main": "src/app.ts", "scripts": {"build:app": "vite build", "start:app": "ags run --gtk4 dist/app.js", "start": "bun build && bun start:app", "dev": "./dev.sh", "dev:debug": "GTK_DEBUG=interactive ./dev.sh", "dev:watch": "killall gjs ags;  nodemon", "validate:types": "tsc --noEmit", "clear": "rm -rf dist @types"}, "keywords": ["ags", "typescript", "gtk"], "author": "<PERSON>", "devDependencies": {"@cspotcode/source-map-support": "^0.8.1", "bun-types": "^1.2.13", "nodemon": "^3.1.10", "sass": "^1.89.0", "source-map": "^0.7.4", "source-map-support": "^0.5.21", "typescript": "^5.8.3", "vite": "^6.3.5"}, "dependencies": {"@girs/gtksource-5": "^5.16.1-4.0.0-beta.23", "@phosphor-icons/core": "^2.1.1", "astal": "/usr/share/astal/gjs", "csstype": "^3.1.3", "tailwindcss": "^3.4.17"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}